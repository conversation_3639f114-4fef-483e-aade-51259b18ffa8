select year_month_str,cvm_instance_type,strategy_type,region_name,sum(cumulative_purchase_core) as sum_core,customhouse_title
from (SELECT
          year_month_str,
          instance_type_from_device_type as cvm_instance_type,
          device_type,
          strategy_type,
          region_name,
          SUM(purchase_core) OVER (
        PARTITION BY instance_type_from_device_type, device_type, strategy_type,region_name,customhouse_title
        ORDER BY year_month_str
        ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) AS cumulative_purchase_core,
          customhouse_title
      FROM
          (select year_month_str,instance_type_from_device_type,device_type,sum(purchase_core) as purchase_core,strategy_type,customhouse_title,
                  if(customhouse_title = '境内' or country_name='美国', region_name, country_name) as region_name
           from cloud_demand_lab.longterm_predict_output_purchase_split
           where task_id=:task_id and split_version_id= :split_version_id and year_month_str between :predict_start and :predict_end
               ${customhouse_title_condition}
               ${region_or_country_condition}
           group by instance_type_from_device_type,device_type,year_month_str,strategy_type,region_name,customhouse_title) a
      ORDER BY
          instance_type_from_device_type,
          device_type,
          region_name,
          strategy_type,
          year_month_str) t
group by year_month_str,cvm_instance_type, strategy_type, region_name,customhouse_title;