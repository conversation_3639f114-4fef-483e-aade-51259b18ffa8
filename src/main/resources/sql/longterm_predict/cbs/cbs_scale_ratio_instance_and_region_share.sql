
SELECT stat_time,
       instance_type,
       cur_disk AS increase,
       customhouse_title,
       region_name
FROM (
         SELECT stat_time,
                instance_type,
                SUM(change_service_disk_from_last_month) AS cur_disk,
                customhouse_title,
                if(customhouse_title = '境外' and country_name!='美国', country_name, region_name) as region_name
         FROM std_crp.dwd_txy_cbs_scale_agg_df
         WHERE stat_time IN (
             SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
             FROM numbers(dateDiff('month', toDate('2020-01-01'), now()) + 1)
             ORDER BY last_day_of_month DESC
             LIMIT 1,12  -- 取最近三个月的最后一天
     )
    AND app_id NOT IN (1258344706, 1251316161)
                    AND instance_type NOT LIKE 'RS%'
                    AND instance_type NOT LIKE 'RM%'
GROUP BY stat_time, instance_type, customhouse_title, region_name
ORDER BY stat_time, instance_type, customhouse_title)
