package cloud.demand.lab.modules.order.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
public class PerformanceTaskReq {

    @NotBlank(message = "订单类型不能为空")
    @Pattern(regexp = "^(新增|弹性)$", message = "订单类型只能为新增或弹性")
    private String oderType;

    @NotBlank(message = "模糊机型不能为空")
    @Pattern(regexp = "^(instanceGroup|instanceType)$", message = "模糊机型只能为instanceGroup或instanceType")
    private String fuzzyInstance;

    @Pattern(regexp = "^(countryName|regionName|zoneName)$", message = "模糊区域只能为countryName或regionName或zoneName")
    @NotBlank(message = "模糊区域不能为空")
    private String fuzzyArea;


}
