package cloud.demand.lab.modules.order.service.filler;

public interface OrderItemPreDeductFiller {

    /**
     * 提供：订单明细号
     */
    String provideOrderNumberId();

    /**
     *  填充字段：申请预扣实例数
     */
    void fillPreDeductNum(Integer preDeductNum);

    /**
     *  填充字段：申请预扣核心数
     */
    void fillPreDeductCore(Integer preDeductCore);

    /**
     *  填充字段：实际预扣实例数
     */
    void fillActualPreDeductNum(Integer actualPreDeductNum);

    /**
     *  填充字段：实际预扣核心数
     */
    void fillActualPreDeductCore(Integer actualPreDeductCore);

}
