package cloud.demand.lab.modules.order.service.filler.handler;

import cloud.demand.lab.common.utils.BatchUtil;
import cloud.demand.lab.modules.operation_view.supply_and_demand.constants.Constant;
import cloud.demand.lab.modules.order.service.filler.InnerFiller;
import cloud.demand.lab.modules.order.service.filler.core.FillerHandler;
import com.pugwoo.dbhelper.DBHelper;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 内部规则：is_inner=1&行业部门=内部业务部
 */
@Service
public class InnerFillerHandler implements FillerHandler<InnerFiller> {

    @Resource
    private DBHelper ckstdcrpDBHelper;

    @Override
    public void fill(List<InnerFiller> obj) {
        // 改为分批操作，提高效率(之前的做法查询dwd_txy_appid_info_cf全表数据量太大了)，降低内存消耗
        BatchUtil.syncBatchExec(obj, 20000, this::fillOneBatch);
    }

    private void fillOneBatch(List<InnerFiller> obj) {
        //dwd_txy_appid_info_cf的uin_type：uin类型，0内部1外部
        String sql = "select toString(uin) as uin, uin_type as uinType "
                + "from dwd_txy_appid_info_cf where uin in (?)";
        List<String> uin = obj.stream().map(item -> item.provideUin())
                .filter(item -> !StringUtils.equals(item,Constant.EMPTY_STR))
                .distinct().collect(Collectors.toList());
        Map<String, Integer> uinMap = ckstdcrpDBHelper.getRaw(Map.class, sql, uin).stream()
                .filter(item -> Objects.nonNull(item.get("uin")))
                .collect(Collectors.toMap(item -> MapUtils.getString(item, "uin"), item -> MapUtils.getInteger(item, "uinType")));
        for (InnerFiller item : obj) {
            //默认是外部
            Integer uinType = uinMap.getOrDefault(item.provideUin(), 1);
            uinType = uinType == -1 ? 1 : uinType;

            if(uinType == 0 || StringUtils.equals(item.provideIndustryDept(), Constant.INNER_INDUSTRY_DEPT)){
                item.fillIsInner(1);
            }else {
                item.fillIsInner(0);
            }
        }
    }
}
