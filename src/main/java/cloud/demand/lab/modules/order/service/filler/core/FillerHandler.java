package cloud.demand.lab.modules.order.service.filler.core;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;

/**
 * 数据填充处理器
 *
 * @param <T> 支持数据填充处理的参数类型，此参数类型暂时只支持接口 <br/>
 * @see FillerService
 */
public interface FillerHandler<T> {

    int customerInfoFillerHandlerOrder = 10;
    int paasProductFillerHandlerOrder = 30;

    int bizTypeFillerHandlerOrder = 40;

    int commonCustomerShortNameFillerHandlerOrder = 100;

    int warZoneByCustomerShortNameFillerHandlerOrder = 120;

    int zoneInfoFillerHandlerOrder = 200;

    int mainZoneFillerHandlerOrder = 210;

    int countryNameFillerHandlerOrder = 220;

    int areaRegionNameFillerHandlerOrder = 240;

    int instanceCategoryFillerHandler = 300;
    /**
     * 数据填充
     */
    void fill(List<T> obj);

    /**
     * 获取对应的 Filler 的类型
     */
    default Class<T> getSupportFiller() {
        // 自动获取范型 T 的类型返回
        Type[] types = getClass().getGenericInterfaces();
        for (Type type : types) {
            if (type instanceof ParameterizedType) {
                ParameterizedType pt = (ParameterizedType) type;
                if (pt.getRawType().equals(FillerHandler.class)) {
                    Type fillerType = pt.getActualTypeArguments()[0];
                    return ((Class<T>) fillerType);
                }
            }
        }
        throw new UnsupportedOperationException(
                "the default 'getSupportFiller' method can not get value, please Override");
    }

    /**
     * 获取执行顺序，多个不同的FilerHandler处理数据时，可能会有依赖关系，通过顺序号控制，值越小越先执行 <br/>
     * 开发人员根据具体的Filler进行判断来确定order值，一个Filler需要填充的字段值是不是另一个Filler所需要的字段值
     */
    default int getExecOrder() {
        return 0;
    }

}
