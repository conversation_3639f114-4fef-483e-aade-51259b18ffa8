package cloud.demand.lab.modules.order.enums;

import java.util.Objects;
import org.nutz.lang.Strings;

public enum OrderTypeEnum {

    NEW("NEW", "新增"),

    ELASTIC("ELASTIC", "弹性");

    private final String code;

    private final String name;

    OrderTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static OrderTypeEnum getByCode(String code) {
        for (OrderTypeEnum e : OrderTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        OrderTypeEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

    public static String getCodeByName(String name) {
        for (OrderTypeEnum e : OrderTypeEnum.values()) {
            if (Strings.equals(name, e.getName())) {
                return e.getCode();
            }
        }
        return "";
    }
    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
