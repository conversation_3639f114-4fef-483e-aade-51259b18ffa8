package cloud.demand.lab.modules.order.service.filler;

/**
 * ppl的区域信息字段的接口定义，用于统一的参数填充<br/>
 * 实现此接口需要在实现类中定义以下的字段<br/>
 * <blockquote><pre>
 *     // 以下是进行数据填充的需要的条件字段
 *
 *     private String zoneName; // 可用区名称
 *
 *
 *     // 以下是待进行数据填充的字段
 *
 *     private String zone; // 可用区编码
 *     private String areaName; // 区域名称
 *     private String customhouseTitle; // 境内外
 *
 * </pre></blockquote><p/>
 */
public interface AreaRegionNameFiller {

    /**
     * 可用区名称
     */
    String provideZoneName();

    /**
     * 填充地域名称
     */
     void fillRegionName(String regionName);

}
