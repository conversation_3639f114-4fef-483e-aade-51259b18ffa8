package cloud.demand.lab.modules.order.dto;

import cloud.demand.lab.common.utils.DateUtils;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import report.utils.anno.WhereReport;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/13 21:48
 */
@Data
public class OrderGridDetailReq {
    @WhereReport
    private List<String> orderNumber;

    @WhereReport
    private List<String> customerUin;

    @WhereReport(sql = " imp_date >= ? ")
    private String startTime;

    @WhereReport(sql = " imp_date <= ? ")
    private String endTime;

    @WhereReport(sql = " plan_instance_type in (?) ")
    private List<String> InstanceType;

    @WhereReport(sql = " plan_zone_name in (?) ")
    private List<String> zoneName;

    public static OrderGridDetailReq build(String yearMonth, List<CrpPplOrderItemAndInfoVO> list) {
        OrderGridDetailReq req = new OrderGridDetailReq();
        req.setOrderNumber(list.stream().map(CrpPplOrderItemAndInfoVO::getOrderNumber).distinct().collect(Collectors.toList()));
        //req.setCustomerUin(list.stream().map(CrpPplOrderItemAndInfoVO::getCustomerUin).collect(Collectors.toList()));
        req.setZoneName(list.stream().map(CrpPplOrderItemAndInfoVO::getZoneName).distinct().collect(Collectors.toList()));
        req.setInstanceType(list.stream().map(CrpPplOrderItemAndInfoVO::getInstanceType).collect(Collectors.toList()));

        LocalDate beginBuyDate = list.stream().map(CrpPplOrderItemAndInfoVO::getBeginBuyDate).min(LocalDate::compareTo).get();
        LocalDate firstDayOfMonth = LocalDate.parse(yearMonth+"-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate startDate = beginBuyDate.isBefore(firstDayOfMonth) ? firstDayOfMonth : beginBuyDate;
        req.setStartTime(startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));

        LocalDate endBuyDate = list.stream().map(CrpPplOrderItemAndInfoVO::getEndBuyDate).max(LocalDate::compareTo).get();
        LocalDate lastDayOfMonth = LocalDate.parse(yearMonth+"-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")).with(TemporalAdjusters.lastDayOfMonth());
        LocalDate endDate = endBuyDate.isAfter(lastDayOfMonth) ? lastDayOfMonth : endBuyDate;
        req.setEndTime(endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        return req;
    }

}
