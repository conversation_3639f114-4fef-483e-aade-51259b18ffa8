package cloud.demand.lab.modules.common_dict.DO;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("paas_product_uin_white_list")
public class PaasProductUinWhiteListDO extends BaseDO {

    @Column(value = "uin")
    private String uin;

    @Column(value = "product")
    private String product;

    @Column(value = "remark")
    private String remark;

}