package cloud.demand.lab.modules.common_dict.DO;

/**
 * 是否为采购机型策略表
 */

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

/**
 * 是否为新机型策略表-行业数据看板
 */
@Data
@ToString
@Table("bas_mrp_v2_generation_instance_type")
public class BasMrpV2GenerationInstanceTypeDO implements IVersionBasTableDO {

    /** 自增 id<br/>Column: [id] */
    @ExcelIgnore
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    @ExcelProperty(value = "实例类型", index = 0)
    private String instanceType;

    /** 是否为采购机型：是，否<br/>Column: [is_purchase] */
    @Column(value = "generation")
    @ExcelProperty(value = "新旧机型", index = 1)
    private String generation;

    /** 版本号：用于区分不同版本记录<br/>Column: [version] */
    @ExcelIgnore
    @Column(value = "version")
    private Long version;

    /** 是否为默认版本，1：是，0：否<br/>Column: [default_flag] */
    @ExcelIgnore
    @Column(value = "default_flag")
    private String defaultFlag;

    /** 创建的时间<br/>Column: [create_time] */
    @ExcelIgnore
    @Column(value = "create_time")
    private Date createTime;

    /** 创建用户<br/>Column: [create_user] */
    @ExcelIgnore
    @Column(value = "create_user")
    private String createUser;
}
