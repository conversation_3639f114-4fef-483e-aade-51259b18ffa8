package cloud.demand.lab.modules.common_dict.http.service;

import cloud.demand.lab.modules.common_dict.http.dto.QCloudZoneReq;
import cloud.demand.lab.modules.common_dict.http.dto.QCloudZoneResp;
import cloud.demand.lab.modules.common_dict.http.header.IdcDataCookieHeader;
import cs.easily.tp.annotation.TpApi;
import cs.easily.tp.annotation.TpBody;
import cs.easily.tp.annotation.TpClient;

@TpClient(baseUrl = "http://idcdata.woa.com", prefix = "openapi/v4/idcdata/logical",apikey = false, desc = "idc数据", readTimeoutSeconds = 60,headerConfigClass = IdcDataCookieHeader.class)
public interface IdcDataHttpService {

    /**
     * @param req 消息体
     */
    @TpApi(path = "query", desc = "查询园区")
    QCloudZoneResp queryQCloudZone(@TpBody QCloudZoneReq req);
}