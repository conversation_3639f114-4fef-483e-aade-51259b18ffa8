package cloud.demand.lab.modules.common_dict.service;

import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekWithDateVO;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ResPlanService {

    /**
     * 根据指定年+周确定唯一的节假周DO
     */
    ResPlanHolidayWeekDO getHolidayWeekInfoByYearWeek(int year, int week);


    /**
     * 获取指定时间的节假周信息
     */
    ResPlanHolidayWeekWithDateVO getHolidayWeekInfoByDate(String dateStr);

    /**
     * 获取指定时间的节假周信息，批量
     *
     * @param dateStrs 日期字符串，格式为yyyy-MM-dd
     * @return 返回的map的key是对应的dateStr
     */
    Map<String, ResPlanHolidayWeekDO> getHolidayWeekInfoByDates(List<String> dateStrs);


}
