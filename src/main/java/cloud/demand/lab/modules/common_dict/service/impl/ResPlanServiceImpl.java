package cloud.demand.lab.modules.common_dict.service.impl;

import cloud.demand.lab.common.config.CacheConfiguration.SynchronizedHiSpeedCache1Second;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekWithDateVO;
import cloud.demand.lab.modules.common_dict.service.ResPlanService;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 */
@Service
public class ResPlanServiceImpl implements ResPlanService {


    @HiSpeedCache(expireSecond = 3600)
    @SynchronizedHiSpeedCache1Second
    public List<ResPlanHolidayWeekDO> getAllHolidayWeekInfos() {
        return ResPlanHolidayWeekDO.db().getAll();
    }


    /**
     * 根据指定年+周确定唯一的节假周DO
     *
     * @param year y
     * @param week w
     */
    @Override
    public ResPlanHolidayWeekDO getHolidayWeekInfoByYearWeek(int year, int week) {
        List<ResPlanHolidayWeekDO> all = SpringUtil.getBean(ResPlanServiceImpl.class).getAllHolidayWeekInfos();
        List<ResPlanHolidayWeekDO> filter = ListUtils.filter(all,
                o -> Objects.equals(o.getYear(), year) && Objects.equals(o.getWeek(), week));
        if (filter.size() != 1) {
            throw new RuntimeException("节假周信息: {year = " + year + " , week = " + week + " }不存在或不唯一");
        }
        return filter.get(0);
    }

    /**
     * 获取指定时间的节假周信息
     *
     * @param dateString string
     */
    @Override
    @HiSpeedCache(expireSecond = 300, continueFetchSecond = 3600, keyScript = "args[0]", cloneReturn = false)
    @SynchronizedHiSpeedCache1Second
    public ResPlanHolidayWeekWithDateVO getHolidayWeekInfoByDate(String dateString) {
        List<ResPlanHolidayWeekDO> all = SpringUtil.getBean(ResPlanServiceImpl.class).getAllHolidayWeekInfos();
        List<ResPlanHolidayWeekDO> filter =
                ListUtils.filter(all, o -> o != null && o.getStart().compareTo(dateString) <= 0
                        && o.getEnd().compareTo(dateString) >= 0);
        if (filter.size() != 1) {
            throw new RuntimeException("节假周信息有误，请查看");
        }
        return new ResPlanHolidayWeekWithDateVO(filter.get(0), LocalDate.parse(dateString));
    }

    /**
     * 获取指定时间的节假周信息，批量
     *
     * @param dateStrings 日期字符串，格式为yyyy-MM-dd
     * @return 返回的map的key是对应的dateStr
     */
    @Override
    public Map<String, ResPlanHolidayWeekDO> getHolidayWeekInfoByDates(List<String> dateStrings) {
        Map<String, ResPlanHolidayWeekDO> map = new HashMap<>();
        for (String dateStr : dateStrings) {
            ResPlanHolidayWeekDO holidayWeekInfoByDate = getHolidayWeekInfoByDate(dateStr);
            map.put(dateStr, holidayWeekInfoByDate);
        }
        return map;
    }


}
