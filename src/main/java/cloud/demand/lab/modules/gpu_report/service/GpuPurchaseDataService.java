package cloud.demand.lab.modules.gpu_report.service;

import cloud.demand.lab.modules.gpu_report.entity.GpuPurchaseDataDO;
import java.io.ByteArrayInputStream;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import report.utils.service.BasVersionCommonService;

/** GPU 采购在途导入数据 */
public interface GpuPurchaseDataService extends BasVersionCommonService<GpuPurchaseDataDO> {

    public ResponseEntity<InputStreamResource> exportExcel();
}
