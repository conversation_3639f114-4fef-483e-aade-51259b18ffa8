package cloud.demand.lab.modules.gpu_report.dto;

import cloud.demand.lab.common.entity.UserPermissionDto;
import cloud.demand.lab.common.utils.LoginUtils;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.gpu_report.where.where_parse.AreaNameParseWhere;
import cloud.demand.lab.modules.gpu_report.where.where_parse.CountryNameParseWhere;
import cloud.demand.lab.modules.gpu_report.where.where_parse.CustomerRangeParseWhere;
import cloud.demand.lab.modules.gpu_report.where.where_parse.CustomerTypeParseWhere;
import cloud.demand.lab.modules.gpu_report.where.where_parse.FuzzyQueryWhereParse;
import cloud.demand.lab.modules.gpu_report.where.where_parse.UnCustomerShortNameParse;
import cloud.demand.lab.modules.order.enums.Ppl13weekProductTypeEnum;
import com.google.common.base.Objects;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import report.utils.anno.WhereReport;
import report.utils.anno.WhereReportGroup;
import report.utils.dto.AbstractDimFieldsReq;

/** GPU专项报表查询
 * group：
 * 1；订单需求：order_demand.sql
 * 2：ppl 为转订单需求：ppl_demand.sql
 * 3：月切规模：scale.sql
 * 4：预扣：withholding.sql
 * 5：采购在途：gpu_purchase.sql
 * */
@Data
public class GpuReportReq extends AbstractDimFieldsReq {

    /** 订单类型，ppl 不用这个，用 product */
    @WhereReportGroup({
            @WhereReport(group = 1,sql = "t2.order_category = ?"),
            @WhereReport(group = 4,sql = "order_category = ?"),
            @WhereReport(group = 3, sql = "product = ?")
    })
    @NotEmpty(message = "产品类型不能为空")
    @Pattern(regexp = "CVM|GPU", message = "产品类型错误，只支持CVM和GPU")
    private String product;


    /** 产品，只有 ppl 用到了 */
    @WhereReportGroup({
            @WhereReport(group = 2,sql = "product in (?)"),
    })
    private List<String> productName;

    // =============== 客户信息 ================

    /** 行业部门 */
    @WhereReportGroup({
            @WhereReport(group = 1,sql = "industry_dept in (?)"),
            @WhereReport(group = 2,sql = "industry_dept in (?)"),
            @WhereReport(group = 4,sql = "industry_dept in (?)"),
            @WhereReport(group = 5,sql = "industry_dept in (?)"),
            @WhereReport(group = 3, sql = "origin_industry_dept in (?)")
    })
    private List<String> industryDept;


    /** 行业部门 （鉴权）*/
    @WhereReportGroup({
            @WhereReport(group = 1,sql = "industry_dept in (?)"),
            @WhereReport(group = 2,sql = "industry_dept in (?)"),
            @WhereReport(group = 4,sql = "industry_dept in (?)"),
            @WhereReport(group = 3, sql = "origin_industry_dept in (?)")
    })
    private List<String> authIndustryDept;

    /** 战区 */
    @WhereReportGroup({
            @WhereReport(group = 1,sql = "war_zone in (?)"),
            @WhereReport(group = 2,sql = "war_zone in (?)"),
            @WhereReport(group = 3,sql = "war_zone in (?)"),
            @WhereReport(group = 4,sql = "war_zone in (?)"),
            @WhereReport(group = 5,sql = "war_zone in (?)") // 通过客户简称补全的字段，允许空值
    })
    private List<String> warZone;

    /** 战区 （鉴权）*/
    @WhereReportGroup({
            @WhereReport(group = 1, sql = "war_zone in (?)"),
            @WhereReport(group = 2, sql = "war_zone in (?)"),
            @WhereReport(group = 3, sql = "war_zone in (?)"),
            @WhereReport(group = 4, sql = "war_zone in (?)")
    })
    private List<String> authWarZone;

    /** 客户简称 */
    @WhereReportGroup({
            @WhereReport(group = 1, sql = "customer_short_name in (?)"),
            @WhereReport(group = 2, sql = "customer_short_name in (?)"),
            @WhereReport(group = 3, sql = "customer_short_name in (?)"),
            @WhereReport(group = 5, sql = "customer_short_name in (?)"),
            @WhereReport(group = 4, sql = "customer_short_name in (?)")
    })
    private List<String> customerShortName;

    /** 通用客户简称 */
    @WhereReportGroup({
            @WhereReport(group = 1, parseParams = {"customer_short_name"}, parsers = UnCustomerShortNameParse.class),
            @WhereReport(group = 2, parseParams = {"customer_short_name"}, parsers = UnCustomerShortNameParse.class),
            @WhereReport(group = 3, sql = "un_customer_short_name in (?)"),
            @WhereReport(group = 5, sql = "un_customer_short_name in (?)"), // 通过客户简称补全的字段，允许空值
            @WhereReport(group = 4, sql = "common_customer_short_name in (?)")
    })
    private List<String> unCustomerShortName;

    /** 客户简称 (鉴权) */
    @WhereReportGroup({
            @WhereReport(group = 1,sql = "customer_short_name in (?)"),
            @WhereReport(group = 2,sql = "customer_short_name in (?)"),
            @WhereReport(group = 3, sql = "un_customer_short_name in (?)"),
            @WhereReport(group = 4, sql = "common_customer_short_name in (?)")
    })
    private List<String> authUnCustomerShortName;

    private Boolean isAllIndustryDept; // 是否为全行业，指标：在线库存，采购在途等产品指标用，需要全行业权限

    /** 客户 uin */
    @WhereReportGroup({
            @WhereReport(group = 1),
            @WhereReport(group = 2),
            @WhereReport(group = 4),
            @WhereReport(group = 3, columnValue = "uin")
    })
    private List<String> customerUin;

    // =============== 地域信息 ================
    /** 可用区 */
    @WhereReportGroup({
            @WhereReport(group = 2),
            @WhereReport(group = 3),
            @WhereReport(group = 1, columnValue = "demand_zone_name"),
            @WhereReport(group = 4, columnValue = "plan_zone_name")
    })
    private List<String> zoneName;

    /** 地域 */
    @WhereReportGroup({
            @WhereReport(group = 2),
            @WhereReport(group = 3),
            @WhereReport(group = 5, sql = "region_name in (?)"), // 非必填字段，允许空值
            @WhereReport(group = 1, columnValue = "demand_region_name"),
            @WhereReport(group = 4, columnValue = "region_name")
    })
    private List<String> regionName;

    /** 区域 */
    @WhereReportGroup({
            @WhereReport(group = 2),
            @WhereReport(group = 5, sql = "area_name in (?)"), // 通过 region_name 自动补全的字段，允许空值
            @WhereReport(group = 1, columnValue = "demand_area_name"),
            @WhereReport(group = 3, parsers = AreaNameParseWhere.class, parseParams = "region_name"),
            @WhereReport(group = 4, parsers = AreaNameParseWhere.class, parseParams = "region_name")
    })
    private List<String> areaName;

    /** 国家 */
    @WhereReportGroup({
            @WhereReport(group = 2, parseParams = "region_name",parsers = CountryNameParseWhere.class),
            @WhereReport(group = 3, parseParams = "region_name",parsers = CountryNameParseWhere.class),
            @WhereReport(group = 4, parseParams = "region_name",parsers = CountryNameParseWhere.class),
            @WhereReport(group = 5),
            @WhereReport(group = 1, parseParams = "demand_region_name",parsers = CountryNameParseWhere.class)
    })
    private List<String> countryName;

    /** 境内外 */
    @WhereReportGroup({
            @WhereReport(group = 2),
            @WhereReport(group = 3),
            @WhereReport(group = 4),
            @WhereReport(group = 5),
            @WhereReport(group = 1, columnValue = "demand_customhouse_title")
    })
    private List<String> customhouseTitle;

    // =============== 实例信息 ================
    /** 实例类型 */
    @WhereReportGroup({
            @WhereReport(group = 2),
            @WhereReport(group = 3),
            @WhereReport(group = 1, columnValue = "demand_instance_type"),
            @WhereReport(group = 4, columnValue = "plan_instance_type")
    })
    private List<String> instanceType;

    /** gpu卡型 */
    @WhereReportGroup({
            @WhereReport(group = 3),
            @WhereReport(group = 5),
            @WhereReport(group = 1, columnValue = "demand_gpu_type"),
            @WhereReport(group = 2, columnValue = "gpu_type"),
            @WhereReport(group = 4, columnValue = "gpu_type")
    })
    private List<String> gpuCardType;


    // =============== 订单信息 ================
    /** 订单号 */
    @WhereReportGroup({
            @WhereReport(group = 1, columnValue = "t1.order_number"),
            @WhereReport(group = 4)
    })
    private List<String> orderNumber;

    /** 应用角色 */
    @WhereReportGroup({
            @WhereReport(group = 1),
            @WhereReport(group = 4)
    })
    private List<String> appRole;

    /** 项目名称 */
    @WhereReportGroup({
            @WhereReport(group = 1, parseParams = {"project_name"}, parsers = FuzzyQueryWhereParse.class),
            @WhereReport(group = 2, parseParams = {"project_name"}, parsers = FuzzyQueryWhereParse.class),
            @WhereReport(group = 4, parseParams = {"project_name"}, parsers = FuzzyQueryWhereParse.class)
    })
    private List<String> projectName;

    /** 订单来源 */
    @WhereReportGroup({
            @WhereReport(group = 1),
            @WhereReport(group = 4)
    })
    private String orderSource;

    /** 订单状态 */
    @WhereReport(group = 1)
    private List<String> orderStatus;

    /** 订单节点 */
    @WhereReportGroup({
            @WhereReport(group = 1),
            @WhereReport(group = 4)
    })
    private List<String> orderNodeCode;

    // =============== 规模信息 ================
    /** 客户范围 */
    @WhereReport(group = 3,columnValue = "is_inner", parsers = CustomerRangeParseWhere.class)
    private List<String> customerRange;
    /** 客户类型 */
    @WhereReport(group = 3,columnValue = "customer_type", parsers = CustomerTypeParseWhere.class)
    private List<String> customerType;

    /** 规模类型：计费用量，服务用量，默认计费用量 */
    private String scaleType = "计费用量";

    @WhereReportGroup({
            @WhereReport(group = 1, sql = "order_label in (?)")
    })
    private List<String> orderLabel; // 订单标签

    /** 起始年月  格式：yyyy-MM */
    @NotBlank(message = "起始年月不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "结束年月格式不正确，例子：2025-01")
    @WhereReportGroup({
            @WhereReport(group = 1, sql = "DATE_FORMAT(consensus_begin_buy_date, '%Y-%m') >= ?"),
            @WhereReport(group = 2, sql = "formatDateTime(begin_buy_date, '%Y-%m') >= ?"),
            @WhereReport(group = 3, sql = "formatDateTime(stat_time, '%Y-%m') >= ?"),
            @WhereReport(group = 4, sql = "formatDateTime(consensus_begin_buy_date, '%Y-%m') >= ?"),
    })
    private String startYearMonth;

    /** 结束年月  格式：yyyy-MM */
    @NotBlank(message = "结束年月不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "结束年月格式不正确，例子：2025-01")
    @WhereReportGroup({
            @WhereReport(group = 1, sql = "DATE_FORMAT(consensus_begin_buy_date, '%Y-%m') <= ?"),
            @WhereReport(group = 2, sql = "formatDateTime(begin_buy_date, '%Y-%m') <= ?"),
            @WhereReport(group = 3, sql = "formatDateTime(stat_time, '%Y-%m') <= ?"),
            @WhereReport(group = 4, sql = "formatDateTime(consensus_begin_buy_date, '%Y-%m') <= ?"),
    })
    private String endYearMonth;

    /** 起始供应年月 */
    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "结束供应年月格式不正确，例子：2025-01")
    @WhereReport(group = 5, sql = "`year_month` >= ?")
    private String startSupplyYearMonth;

    /** 结束供应年月  格式：yyyy-MM */
    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "结束供应年月格式不正确，例子：2025-01")
    @WhereReport(group = 5, sql = "`year_month` <= ?")
    private String endSupplyYearMonth;

    public Collection<String> buildFilter() {
        return null;
    }

    public void fill(){
        // 供应年月保底用 起始年月 结束年月
        if (StringUtils.isBlank(startSupplyYearMonth)){
            this.startSupplyYearMonth = startYearMonth;
        }
        if (StringUtils.isBlank(endSupplyYearMonth)){
            this.endSupplyYearMonth = endYearMonth;
        }
        fillProduct();
    }

    /**
     * 填充产品
     */
    public void fillProduct(){
        String orderCategory = getProduct();
        List<String> product = new ArrayList<>();
        if (Objects.equal(orderCategory, Ppl13weekProductTypeEnum.GPU.getCode())){
            product.add(Ppl13weekProductTypeEnum.GPU.getName());
        }else if ( Objects.equal(orderCategory, Ppl13weekProductTypeEnum.CVM.getCode())){
            product.add(Ppl13weekProductTypeEnum.CVM.getName());
            product.addAll(Ppl13weekProductTypeEnum.getPaasProductNameList());
        }
        this.setProductName(product);
    }

    /** 鉴权信息 */
    public boolean auth() {
        String userNameWithSystem = LoginUtils.getUserNameWithSystem();
        DictService dict = SpringUtil.getBean(DictService.class);
        Boolean adminUserOrNot = dict.checkIsAdmin(userNameWithSystem);
        if (adminUserOrNot) {
            this.setIsAllIndustryDept(true);
            return true;
        }
        // 如果不是管理员 需要判断是否有权限看数据
        UserPermissionDto permissionByUserAndRole = dict.getPermissionByUserAndRole(
                IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER.getCode(), userNameWithSystem);
        if (permissionByUserAndRole == null){
            return false;
        }
        // 是否有 GPU 的权限（全产品 or 包含 GPU(裸金属&CVM)）
        String product = this.getProduct();
        if (Objects.equal(product, Ppl13weekProductTypeEnum.GPU.getCode())){
            boolean hasGpuPermission = permissionByUserAndRole.getIsAllProduct() || permissionByUserAndRole.getProduct().contains(Ppl13weekProductTypeEnum.GPU.getName());
            if (!hasGpuPermission){
                return false;
            }
        }else {
            boolean hasCvmPermission = permissionByUserAndRole.getIsAllProduct() || permissionByUserAndRole.getProduct().contains(Ppl13weekProductTypeEnum.CVM.getName());
            if (!hasCvmPermission){
                return false;
            }
        }

        this.setIsAllIndustryDept(permissionByUserAndRole.getIsAllIndustry()); // 查询产品指标时用
        
        if (!permissionByUserAndRole.getIsAllIndustry()){
            this.setAuthIndustryDept(permissionByUserAndRole.getIndustry());
        }

        // 目前只要行业粒度，后面暂时不加

//        if (!permissionByUserAndRole.getIsAllWarZone()){
//            this.setAuthWarZone(permissionByUserAndRole.getWarZone());
//        }
//        if (!permissionByUserAndRole.getIsAllCustomer()){
//            this.setAuthUnCustomerShortName(permissionByUserAndRole.getCustomer());
//        }
        return true;
    }

    public void configWithQueryAll(){
        this.setFields(null); // 忽略字段
        this.configWithOutPage();
    }

    public void configWithOutPage(){
        this.setPage(null); // 不分页
        this.setOrderByDim(null); // 不排序
        this.setOrderByField(null);
    }
}
