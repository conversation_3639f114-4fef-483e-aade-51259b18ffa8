package cloud.demand.lab.modules.gpu_report.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import lombok.Data;

/** gpu 排行请求 */
@Data
public class GpuRankReq extends GpuReportReq{
    private Integer top; // 不传默认全部，非 top 用其他表示，比如 top3，则返回xx,xx,xx，其他

    @NotBlank(message = "排序指标不能为空")
    @Pattern(regexp = "cpuNumber|gpuNumber", message = "指标只能为cpuNumber或gpuNumber")
    private String indexField; // 指标：cpuNumber or gpuNumber
}
