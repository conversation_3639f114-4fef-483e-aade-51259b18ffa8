package cloud.demand.lab.modules.gpu_report.model;

import cloud.demand.lab.modules.gpu_report.model.field.ICpuNumber;
import cloud.demand.lab.modules.gpu_report.model.field.IGpuNumber;
import cloud.demand.lab.modules.gpu_report.model.field.IGridStatus;
import cloud.demand.lab.modules.gpu_report.model.field.ISourcePpl;
import cloud.demand.lab.modules.gpu_report.model.field.ISubmitTime;
import cloud.demand.lab.modules.gpu_report.model.field.IUnCustomerShortName;
import report.utils.model.field.customer.*;
import report.utils.model.field.date.IYearMonth;
import report.utils.model.field.instance.*;
import report.utils.model.field.order.*;
import report.utils.model.field.scale.*;
import report.utils.model.field.zone.*;

/** GPU */
public interface IGpuItem extends
        IIndustryDept, IWarZone, ICustomerUin, ICustomerShortName, IUnCustomerShortName, IZoneName, IRegionName, IAreaName, ICountryName,
        ICustomhouseTitle, IInstanceType, IGpuCardType, ISubmitTime,
        IOrderNumber, IAppRole, IProjectName, IOrderSource, IOrderStatus, IOrderNodeCode, ISourcePpl, IGridStatus, ICustomerRange,
        IBeginBuyDate, IEndBuyDate, IGpuNumber, ICpuNumber,
        ICustomerType, IYearMonth {

}
