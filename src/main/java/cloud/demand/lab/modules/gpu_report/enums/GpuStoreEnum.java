package cloud.demand.lab.modules.gpu_report.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import report.utils.model.enums.IReportStoreNameEnum;

@Getter
@AllArgsConstructor
public enum GpuStoreEnum implements IReportStoreNameEnum {
    GPU_INIT("GPU_INIT","GPU-初始化"),
    GPU_ORDER_DEMAND("GPU_ORDER_DEMAND","GPU-订单需求"),
    GPU_PPL_DEMAND("GPU_PPL_DEMAND","GPU-未转单PPL"),
    GPU_SCALE("GPU_SCALE","GPU-已购买"),
    GPU_WITHHOLDING("GPU_WITHHOLDING","GPU-已预扣"),
    GPU_INVENTORY("GPU_INVENTORY","GPU-在线库存"),
    GPU_PURCHASING("GPU_PURCHASING","GPU-采购在途"),

    ;

    private final String name;
    private final String desc;
}
