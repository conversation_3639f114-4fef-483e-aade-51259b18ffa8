package cloud.demand.lab.modules.gpu_report.where.where_parse;

import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.service.DictService;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import report.utils.query.IWhereParser;
import report.utils.query.WhereBuilder.WhereInfo;

/** 国家转地域 */
public class CountryNameParseWhere implements IWhereParser {

    @Override
    public void parseSQL(WhereSQL content, WhereInfo whereInfo, Object t) {
        String[] parseParams = whereInfo.getParseParams();
        Object v = whereInfo.getV();
        String regionNameColumn = parseParams[0]; // 地域字段
        if (v instanceof List){
            List<String> ls = (List<String>) v;
            if (ListUtils.isNotEmpty(ls)){
                // 国家转地域
                Map<String, List<String>> country2RegionMapping = SpringUtil.getBean(DictService.class)
                        .getCountry2RegionMapping();
                List<String> regionNameList = new ArrayList<>();
                for (String countryName : ls){
                    List<String> list = country2RegionMapping.get(countryName);
                    if (ListUtils.isNotEmpty(list)){
                        regionNameList.addAll(list);
                    }
                }
                content.andIf(ListUtils.isNotEmpty(regionNameList),regionNameColumn + " in (?)", regionNameList);
            }
        }
    }
}
