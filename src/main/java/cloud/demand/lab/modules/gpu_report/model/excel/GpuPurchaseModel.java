package cloud.demand.lab.modules.gpu_report.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import java.math.BigDecimal;
import lombok.Data;

/** 采购在途 */
@Data
public class GpuPurchaseModel {
    @ExcelProperty(value = "境内外", index = 0)
    private String customhouseTitle;
    @ExcelProperty(value = "国家", index = 1)
    private String countryName;
    @ExcelProperty(value = "区域", index = 2)
    private String areaName;
    @ExcelProperty(value = "地域", index = 3)
    private String regionName;

    // =============== 实例信息 ================
    @ExcelProperty(value = "GPU卡型", index = 4)
    private String gpuCardType;
    @ExcelProperty(value = "年月", index = 5)
    private String yearMonth;
    /**
     * gpu 卡数
     */
    @ExcelProperty(value = "GPU卡数", index = 6)
    private BigDecimal gpuNumber;
}
