package cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task;

import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputArgsDO;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InputArgsDTO {

    /**策略类型，详见 StrategyTypeEnum */
    private String strategyType;
    /**时间名称*/
    private String dateName;
    /**开始时间范围，含当天*/
    private String startDate;
    /**结束时间范围，含当天*/
    private String endDate;
    /**净增长，不含百分比*/
    private BigDecimal scaleGrowthRate;
    /**替换比例，不含百分比*/
    private BigDecimal replaceRate;
    /**采购系数，不含百分比*/
    private BigDecimal purchaseRate;
    /**备注*/
    private String note;
    /**上传附件*/
    private List<LongtermPredictInputArgsDO.Attachment> attachment;

    public static LongtermPredictInputArgsDO trans(InputArgsDTO args, Long taskId) {
        LongtermPredictInputArgsDO longtermPredictInputArgsDO = new LongtermPredictInputArgsDO();
        longtermPredictInputArgsDO.setTaskId(taskId);
        longtermPredictInputArgsDO.setStrategyType(args.getStrategyType());
        longtermPredictInputArgsDO.setDateName(args.getDateName());
        longtermPredictInputArgsDO.setStartDate(DateUtils.parseLocalDate(args.getStartDate()));
        longtermPredictInputArgsDO.setEndDate(DateUtils.parseLocalDate(args.getEndDate()));
        longtermPredictInputArgsDO.setScaleGrowthRate(args.getScaleGrowthRate());
        longtermPredictInputArgsDO.setReplaceRate(args.getReplaceRate());
        longtermPredictInputArgsDO.setPurchaseRate(args.getPurchaseRate());
        longtermPredictInputArgsDO.setNote(args.getNote());
        longtermPredictInputArgsDO.setAttachment(args.getAttachment());
        return longtermPredictInputArgsDO;
    }

    public static InputArgsDTO from(LongtermPredictInputArgsDO inputArgsDO) {
        InputArgsDTO inputArgsDTO = new InputArgsDTO();
        inputArgsDTO.setStrategyType(inputArgsDO.getStrategyType());
        inputArgsDTO.setDateName(inputArgsDO.getDateName());
        inputArgsDTO.setStartDate(String.valueOf(inputArgsDO.getStartDate()));
        inputArgsDTO.setEndDate(String.valueOf(inputArgsDO.getEndDate()));
        inputArgsDTO.setScaleGrowthRate(inputArgsDO.getScaleGrowthRate());
        inputArgsDTO.setReplaceRate(inputArgsDO.getReplaceRate());
        inputArgsDTO.setPurchaseRate(inputArgsDO.getPurchaseRate());
        inputArgsDTO.setNote(inputArgsDO.getNote());
        inputArgsDTO.setAttachment(inputArgsDO.getAttachment());
        return inputArgsDTO;
    }

}
