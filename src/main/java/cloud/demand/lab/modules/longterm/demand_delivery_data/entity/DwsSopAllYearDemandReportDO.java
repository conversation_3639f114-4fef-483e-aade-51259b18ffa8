package cloud.demand.lab.modules.longterm.demand_delivery_data.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ToString
@Table("dws_sop_all_year_demand_report")
public class DwsSopAllYearDemandReportDO {

    /** 年周（分区键）<br/>Column: [year_week] */
    @Column(value = "year_week")
    private String yearWeek;

    /** 年份<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    /** 周数（全年第 n 周）<br/>Column: [week] */
    @Column(value = "week")
    private Integer week;

    /** 版本时间，没有采购预测版本号的是切片时间，否则为采购预测版本号时间<br/>Column: [version_date] */
    @Column(value = "version_date")
    private LocalDate versionDate;

    /** 采购预测版本号，对应 sop 版本号，非 sop 数据该字段为空值<br/>Column: [version] */
    @Column(value = "version")
    private String version;

    /** 需求类型 <br/>Column: [demand_type] */
    @Column(value = "demand_type")
    private String demandType;

    /** 资源类型：CVM or 物理机<br/>Column: [resource_type] */
    @Column(value = "resource_type")
    private String resourceType;

    /** 业务类型：云业务 or 自研业务 or 自研上云<br/>Column: [business_type] */
    @Column(value = "business_type")
    private String businessType;

    /** 自定义BG名<br/>Column: [custom_bg_name] */
    @Column(value = "custom_bg_name")
    private String customBgName;

    /** 部门名<br/>Column: [dept_name] */
    @Column(value = "dept_name")
    private String deptName;

    /** 规划产品<br/>Column: [plan_product_name] */
    @Column(value = "plan_product_name")
    private String planProductName;

    /** 项目类型<br/>Column: [obs_project_type] */
    @Column(value = "obs_project_type")
    private String obsProjectType;

    /** 指标年<br/>Column: [index_year] */
    @Column(value = "index_year")
    private Integer indexYear;

    /** 指标周<br/>Column: [index_week] */
    @Column(value = "index_week")
    private Integer indexWeek;

    /** cvm 机型族<br/>Column: [instance_family] */
    @Column(value = "instance_family")
    private String instanceFamily;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 物理机机型族<br/>Column: [device_family] */
    @Column(value = "device_family")
    private String deviceFamily;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 未执行台数<br/>Column: [not_executed_num] */
    @Column(value = "not_executed_num")
    private BigDecimal notExecutedNum;

    /** 已执行台数<br/>Column: [executed_num] */
    @Column(value = "executed_num")
    private BigDecimal executedNum;

    /** 总台数<br/>Column: [total_num] */
    @Column(value = "total_num")
    private BigDecimal totalNum;

    /** 未执行核心数<br/>Column: [not_executed_core_num] */
    @Column(value = "not_executed_core_num")
    private BigDecimal notExecutedCoreNum;

    /** 已执行核心数<br/>Column: [executed_core_num] */
    @Column(value = "executed_core_num")
    private BigDecimal executedCoreNum;

    /** 总核心数<br/>Column: [total_core_num] */
    @Column(value = "total_core_num")
    private BigDecimal totalCoreNum;

    /** 是否来自 sop<br/>Column: [is_from_sop] */
    @Column(value = "is_from_sop")
    private Integer isFromSop;


}