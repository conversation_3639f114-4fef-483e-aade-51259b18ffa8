package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputBigCustomerChangeDO;
import cloud.demand.lab.modules.longterm.cos.service.CosBigCustomerHistoryChangeService;
import cloud.demand.lab.modules.longterm.cos.service.CosAlgorithmPredictService;
import cloud.demand.lab.modules.longterm.cos.web.dto.BigCustomerHistoryChangeDTO;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryBigCustomerHistoryChangeReq;
import cloud.demand.lab.modules.longterm.cos.web.req.ReCalculateAlgorithmPredictReq;
import cloud.demand.lab.modules.longterm.cos.web.req.SaveBigCustomerHistoryChangeReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryBigCustomerHistoryChangeResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.SaveBigCustomerHistoryChangeResp;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CosBigCustomerHistoryChangeServiceImpl implements CosBigCustomerHistoryChangeService {

    @Resource
    private DBHelper cdLabDbHelper;
    @Resource
    private CosAlgorithmPredictService cosAlgorithmPredictService;

    @Override
    public QueryBigCustomerHistoryChangeResp queryBigCustomerHistoryChange(QueryBigCustomerHistoryChangeReq req) {
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("category_id = ?", req.getCategoryId());
        whereSQL.and("task_id = ?", req.getTaskId());
        whereSQL.addOrderBy("start_date", "customer_name");

        List<CosLongtermPredictInputBigCustomerChangeDO> doList = cdLabDbHelper.getAll(
                CosLongtermPredictInputBigCustomerChangeDO.class,
                whereSQL.getSQL(),
                whereSQL.getParams()
        );

        // 转换DO为DTO
        List<BigCustomerHistoryChangeDTO> dtoList = ListUtils.transform(doList, BigCustomerHistoryChangeDTO::fromDO);

        QueryBigCustomerHistoryChangeResp resp = new QueryBigCustomerHistoryChangeResp();
        resp.setDataList(dtoList);
        return resp;
    }

    @Override
    public SaveBigCustomerHistoryChangeResp saveBigCustomerHistoryChange(SaveBigCustomerHistoryChangeReq req) {
        Long categoryId = req.getCategoryId();
        Long taskId = req.getTaskId() != null ? req.getTaskId() : 0L;

        // 1. 使用WhereSQL工具构造删除条件
        WhereSQL deleteWhereSQL = new WhereSQL();
        deleteWhereSQL.and("category_id = ?", categoryId);
        deleteWhereSQL.and("task_id = ?", taskId);

        cdLabDbHelper.delete(CosLongtermPredictInputBigCustomerChangeDO.class,
                deleteWhereSQL.getSQL(), deleteWhereSQL.getParams());

        // 2. 转换DTO为DO并设置数据的categoryId和taskId，然后批量插入
        if (req.getDataList() != null && !req.getDataList().isEmpty()) {
            List<CosLongtermPredictInputBigCustomerChangeDO> doList = ListUtils.transform(req.getDataList(), dto -> {
                CosLongtermPredictInputBigCustomerChangeDO dO = dto.toDO();
                dO.setCategoryId(categoryId);
                dO.setTaskId(taskId);
                dO.setId(null); // 清空ID，让数据库自动生成
                return dO;
            });

            cdLabDbHelper.insertBatchWithoutReturnId(doList);
        }

        // 3. 修改完大客户信息后，默认自动重跑预测
        if (req.getAutoReCalPredict() != null && req.getAutoReCalPredict()) {
            ReCalculateAlgorithmPredictReq calReq = new ReCalculateAlgorithmPredictReq();
            calReq.setCategoryId(categoryId);
            calReq.setTaskId(taskId);
            cosAlgorithmPredictService.reCalculateAlgorithmPredict(calReq);
        }

        SaveBigCustomerHistoryChangeResp resp = new SaveBigCustomerHistoryChangeResp();
        resp.setIsSuccess(true);
        return resp;
    }
}
