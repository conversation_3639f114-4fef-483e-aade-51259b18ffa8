package cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task;

import lombok.Data;

/**
 * 操作输入参数备注的请求体
 */
@Data
public class OperateInputArgsTrendNoteReq {

    /**方案id*/
    private Long categoryId;

    /**备注哪条线，这里使用线的变量名称来，目前支持：purchaseRate采购系数，replaceRate替换比例*/
    private String noteLine;

    /**备注月份*/
    private String statTime;

    /**备注内容*/
    private String note;

    /**是否请求删除该条备注*/
    private Boolean isRequestDelete;

}
