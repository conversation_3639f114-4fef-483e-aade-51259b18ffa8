package cloud.demand.lab.modules.longterm.cos.web.req;

import cloud.demand.lab.modules.longterm.cos.web.dto.InputArgsDTO;
import lombok.Data;

import java.util.List;

@Data
public class CreatePredictTaskReq {

    /**方案id*/
    private Long categoryId;
    /**是否直接设置为启用，如果没有传，默认不启用*/
    private Boolean isEnable;

    /**当前创建页面的任务id，如果没有请传0*/
    private Long refTaskId;

    /**方案输入参数*/
    private List<InputArgsDTO> inputArgs;

}
