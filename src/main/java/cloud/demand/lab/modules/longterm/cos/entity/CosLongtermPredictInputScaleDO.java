package cloud.demand.lab.modules.longterm.cos.entity;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * cos中长期剔除大客户后实际用于模型预测的存量
 */
@Data
@ToString
@Table("cos_longterm_predict_input_scale")
public class CosLongtermPredictInputScaleDO extends BaseDO {

    /** 任务id<br/>Column: [task_id] */
    @Column(value = "task_id")
    private Long taskId;

    /** 日期<br/>Column: [date] */
    @Column(value = "date")
    private LocalDate date;

    /** 是否外部客户，0表示否，1表示是，-1表示全部客户<br/>Column: [is_out_customer] */
    @Column(value = "is_out_customer")
    private Integer isOutCustomer;

    /** 当前存量(单位PB)<br/>Column: [cur_scale] */
    @Column(value = "cur_scale")
    private BigDecimal curScale;

}