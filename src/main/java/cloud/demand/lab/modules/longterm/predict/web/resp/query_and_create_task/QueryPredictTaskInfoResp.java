package cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task;

import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictTaskDO;
import cloud.demand.lab.modules.longterm.predict.enums.LongtermPredictCategoryTypeEnum;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.InputArgsDTO;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;

import java.util.List;

@Data
public class QueryPredictTaskInfoResp {

    /**当前任务是否处于启用状态*/
    private Boolean isEnable;

    /**任务实际创建时间*/
    private String createTime;
    /**预测开始时间*/
    private String predictStart;
    /**预测结束时间*/
    private String predictEnd;
    /**预测模型*/
    private String forecastModel;
    /**预测时间颗粒度*/
    private String predictTimeInterval;
    /**预测粒度*/
    private String dimsName;
    /**客户范围*/
    private String scopeCustomer;
    /**资源范围*/
    private String scopeResourcePool;
    /**产品范围*/
    private String scopeProduct;

    /**方案输入参数*/
    private List<InputArgsDTO> inputArgs;

    public static QueryPredictTaskInfoResp from(LongtermPredictTaskDO taskDO,
                                                LongtermPredictCategoryConfigDO categoryDO,
                                                List<InputArgsDTO> inputArgs) {
        QueryPredictTaskInfoResp queryPredictTaskInfoResp = new QueryPredictTaskInfoResp();
        queryPredictTaskInfoResp.setIsEnable(taskDO.getIsEnable());
        queryPredictTaskInfoResp.setCreateTime(DateUtils.format(taskDO.getCreateTime()));
        queryPredictTaskInfoResp.setPredictStart(DateUtils.format(taskDO.getPredictStart(), "yyyy-MM"));
        queryPredictTaskInfoResp.setPredictEnd(DateUtils.format(taskDO.getPredictEnd(), "yyyy-MM"));

        String forecastModel = "";
        if (categoryDO != null) {
            LongtermPredictCategoryTypeEnum typeEnum = LongtermPredictCategoryTypeEnum.getByCode(categoryDO.getCategoryType());
            if (typeEnum != null) {
                forecastModel = typeEnum.getName();
            }
        }
        queryPredictTaskInfoResp.setForecastModel(forecastModel);

        queryPredictTaskInfoResp.setPredictTimeInterval("月");
        queryPredictTaskInfoResp.setDimsName(taskDO.getDimsName());
        queryPredictTaskInfoResp.setScopeCustomer(taskDO.getScopeCustomer());
        queryPredictTaskInfoResp.setScopeResourcePool(taskDO.getScopeResourcePool());
        queryPredictTaskInfoResp.setScopeProduct(taskDO.getScopeProduct());
        queryPredictTaskInfoResp.setInputArgs(inputArgs);
        return queryPredictTaskInfoResp;
    }

}
