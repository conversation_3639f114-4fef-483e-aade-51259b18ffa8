package cloud.demand.lab.modules.longterm.predict.entity;

import cloud.demand.lab.common.config.DBList;
import cloud.demand.lab.common.config.DBList.DBHelperWithClz;
import cloud.demand.lab.common.entity.BaseDO;
import cloud.demand.lab.modules.longterm.predict.entity.SplitRateKeyGetters.SplitZoneNameKeyGetter;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("longterm_predict_config_instance_family_to_zone_name_rate")
public class LongtermPredictConfigInstanceFamilyToZoneNameRateDO extends BaseDO
        implements SplitZoneNameKeyGetter {

    public static DBHelperWithClz<LongtermPredictConfigInstanceFamilyRateDO> db() {
        return DBHelperWithClz.create(DBList.cdLabDbHelper);
    }

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /**
     * 机型大类<br/>Column: [instance_family]
     */
    @Column(value = "instance_family")
    private String instanceFamily;

    /**
     * 地域名称<br/>Column: [region_name]
     */
    @Column(value = "region_or_country")
    private String regionOrCountry;

    /**
     * 可用区名称<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;

    /**
     * 占比<br/>Column: [rate]
     */
    @Column(value = "rate")
    private BigDecimal rate;

}