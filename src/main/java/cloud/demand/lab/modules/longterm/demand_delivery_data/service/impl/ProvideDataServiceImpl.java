package cloud.demand.lab.modules.longterm.demand_delivery_data.service.impl;

import cloud.demand.lab.modules.longterm.demand_delivery_data.dto.DeliveryDataDTO;
import cloud.demand.lab.modules.longterm.demand_delivery_data.entity.LongtermDemandAndExecuteDataCkDO;
import cloud.demand.lab.modules.longterm.demand_delivery_data.entity.LongtermDemandAndExecuteDataDO;
import cloud.demand.lab.modules.longterm.demand_delivery_data.entity.LongtermDemandAndExecuteMachineTypeDictDO;
import cloud.demand.lab.modules.longterm.demand_delivery_data.entity.LongtermDemandAndExecuteWeekDictDO;
import cloud.demand.lab.modules.longterm.demand_delivery_data.entity.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.longterm.demand_delivery_data.service.DeliveryDataService;
import cloud.demand.lab.modules.longterm.demand_delivery_data.service.DemandDataService;
import cloud.demand.lab.modules.longterm.demand_delivery_data.service.ProvideDataService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class ProvideDataServiceImpl implements ProvideDataService {

    @Resource
    private DBHelper resplanDBHelper;
    @Resource
    private DBHelper cdLabDbHelper;
    @Resource
    private DBHelper ckcldDBHelper;
    @Resource
    private DBHelper ckstdcrpDBHelper;

    @Resource
    private DeliveryDataService deliveryDataService;
    @Resource
    private DemandDataService demandDataService;

    @Override
    @Transactional(value = "cdlabTransactionManager")
    public void generateLongtermDemandAndExecuteDataDO() {
        List<LongtermDemandAndExecuteDataDO> result = new ArrayList<>();

        Map<String, String> machineTypeGeneralMap = getMachineTypeGeneral();


        // 1. 查询全量的节假周的数据
        List<ResPlanHolidayWeekDO> holidays = resplanDBHelper.getAll(ResPlanHolidayWeekDO.class, "order by year,week");
        Map<Integer, List<ResPlanHolidayWeekDO>> yearToWeeks = ListUtils.toMapList(holidays, o -> o.getYear(), o -> o);

        // 2. 处理物理机和cvm完成数 从2019年起
        for (ResPlanHolidayWeekDO weekDO : holidays) {
            if (weekDO.getYear() < 2019 || weekDO.getEnd().isAfter(LocalDate.now())) {
                continue;
            }
            {
                List<DeliveryDataDTO> utilWeek = deliveryDataService.getPhysicalData(weekDO.getYear(), weekDO.getEnd().toString(), yearToWeeks.get(weekDO.getYear()));
                List<DeliveryDataDTO> totalYear = deliveryDataService.getPhysicalData(weekDO.getYear(), weekDO.getYear() + "-12-31", yearToWeeks.get(weekDO.getYear()));
                result.addAll(trans("物理机", weekDO, utilWeek, totalYear));
            }
            {
                List<DeliveryDataDTO> utilWeek = deliveryDataService.getCvmData(weekDO.getYear(), weekDO.getEnd().toString(), yearToWeeks.get(weekDO.getYear()));
                List<DeliveryDataDTO> totalYear = deliveryDataService.getCvmData(weekDO.getYear(), weekDO.getYear() + "-12-31", yearToWeeks.get(weekDO.getYear()));
                result.addAll(trans("CVM", weekDO, utilWeek, totalYear));
            }
        }

        /* 3. 需求数据，子淋提供*/
        List<LongtermDemandAndExecuteDataDO> demands = demandDataService.getAllDemand();

        //回退数据
        List<LongtermDemandAndExecuteDataDO> returns = demandDataService.getAllReturn();
        returns = handleCumulative(returns); // 计算Cumulative

        // 4. 整合数据
        result = merge(result, demands);
        result.addAll(returns);
        result.stream().forEach((o)->o.setMachineTypeFamilyGeneral(machineTypeGeneralMap.get(o.getMachineTypeFamily())));


        // 5. 完成
        cdLabDbHelper.executeRaw("delete from longterm_demand_and_execute_data");
        ckcldDBHelper.executeRaw("truncate table cloud_demand.dws_longterm_demand_and_execute_data_local ON CLUSTER default_cluster");
        try {
            Thread.sleep(10000);
        } catch (InterruptedException ignored) {
        }
        List<List<LongtermDemandAndExecuteDataDO>> partition = ListUtils.partition(result, 100000);
        for (List<LongtermDemandAndExecuteDataDO> p : partition) {
            cdLabDbHelper.insertBatchWithoutReturnId(p);
            List<LongtermDemandAndExecuteDataCkDO> p2 = ListUtils.transform(p,
                    o -> JSON.parse(JSON.toJson(o), LongtermDemandAndExecuteDataCkDO.class));
            ckcldDBHelper.insertBatchWithoutReturnId(p2);
        }
    }

    @Override
    @Transactional(value = "cdlabTransactionManager")
    public void generateWeekDict() {
        List<LongtermDemandAndExecuteWeekDictDO> result = new ArrayList<>();

        // 1. 查询最终生成的底表的所有年周
        List<Map> weeks = cdLabDbHelper.getRaw(Map.class,
                " select distinct year,week_num from longterm_demand_and_execute_data "
                    + "   order by year,week_num");
        result = ListUtils.transform(weeks, o -> {
            LongtermDemandAndExecuteWeekDictDO d = new LongtermDemandAndExecuteWeekDictDO();
            d.setYear(NumberUtils.parseInt(o.get("year")));
            d.setWeek(NumberUtils.parseInt(o.get("week_num")));
            return d;
        });
        // 2. 找节假周的数据吗，对于周为负数的不处理
        List<ResPlanHolidayWeekDO> holidays = resplanDBHelper.getAll(ResPlanHolidayWeekDO.class, "order by year,week");
        Map<String, ResPlanHolidayWeekDO> yearToWeeks = ListUtils.toMap(holidays,
                o -> StringTools.join("-", o.getYear(), o.getWeek()), o -> o);
        result.forEach(o -> {
            ResPlanHolidayWeekDO weekDO = yearToWeeks.get(StringTools.join("-", o.getYear(), o.getWeek()));
            if (weekDO != null) {
                o.setWeekStart(weekDO.getStart().toString());
                o.setWeekEnd(weekDO.getEnd().toString());
                o.setDeliveryVersionDate(weekDO.getEnd().toString());
            }
        });

        // 3. 找子淋提供的数据里的版本号
        List<Map> demandVersion = ckstdcrpDBHelper.getRaw(Map.class,
                "select distinct year,week,version_date from dws_sop_all_year_demand_report");
        Map<String, String> demandVersionMap = ListUtils.toMap(demandVersion,
                o -> StringTools.join("-", o.get("year").toString(), o.get("week").toString()),
                o -> o.get("version_date").toString());
        result.forEach(o -> {
            String key = StringTools.join("-", o.getYear(), o.getWeek());
            o.setDemandVersionDate(demandVersionMap.getOrDefault(key, ""));
        });

        // 4. 整合并插入数据库
        cdLabDbHelper.executeRaw("delete from longterm_demand_and_execute_week_dict");
        cdLabDbHelper.insertBatchWithoutReturnId(result);
    }

    /**计算Cumulative的值*/
    private List<LongtermDemandAndExecuteDataDO> handleCumulative(List<LongtermDemandAndExecuteDataDO> returns) {
        Map<String, List<LongtermDemandAndExecuteDataDO>> map = ListUtils.toMapList(returns,
                LongtermDemandAndExecuteDataDO.mergeKeyForCumulative, o -> o);

        List<LongtermDemandAndExecuteDataDO> needFill = new ArrayList<>();
        for (List<LongtermDemandAndExecuteDataDO> list : map.values()) {
            ListUtils.sortAscNullLast(list, LongtermDemandAndExecuteDataDO::getWeekNum);
            BigDecimal cumulativeCore = BigDecimal.ZERO;
            BigDecimal cumulativeUnit = BigDecimal.ZERO;
            for (LongtermDemandAndExecuteDataDO d : list) {
                cumulativeCore = cumulativeCore.add(d.getAnualTotalDeliveryCore());
                cumulativeUnit = cumulativeUnit.add(d.getAnualTotalDeliveryUnit());
                d.setAnualCumulativeDeliveryCore(cumulativeCore);
                d.setAnualCumulativeDeliveryUnit(cumulativeUnit);
            }

            // 最后再把最新完成数设置到anual_total_delivery_core，anual_total_delivery_unit，这个是为了保持和新增的值逻辑一致
            // 也就是说，AnualTotalDeliveryCore和AnualTotalDeliveryUnit 与 week_num无关，它是最新数据
            for (LongtermDemandAndExecuteDataDO d : list) {
                d.setAnualTotalDeliveryCore(cumulativeCore);
                d.setAnualTotalDeliveryUnit(cumulativeUnit);
            }

            // 如果退回的weekNum未补全到1，则补全
            if (list.get(0).getWeekNum() > 1) {
                for (int i = 1; i < list.get(0).getWeekNum(); i++) {
                    LongtermDemandAndExecuteDataDO d = JSON.clone(list.get(0));
                    d.setId(null);
                    d.setWeekNum(i);
                    d.setAnualTotalDemandCore(BigDecimal.ZERO);
                    d.setAnualTotalDemandUnit(BigDecimal.ZERO);
                    d.setAnualTotalDeliveryCore(cumulativeCore);
                    d.setAnualTotalDeliveryUnit(cumulativeUnit);
                    d.setAnualCumulativeDeliveryCore(BigDecimal.ZERO);
                    d.setAnualCumulativeDeliveryUnit(BigDecimal.ZERO);
                    needFill.add(d);
                }
            }
        }

        needFill.addAll(returns);
        return needFill;
    }

    private List<LongtermDemandAndExecuteDataDO> merge(List<LongtermDemandAndExecuteDataDO> delivery,
                                                       List<LongtermDemandAndExecuteDataDO> demand) {
        return ListUtils.merge(delivery, demand, LongtermDemandAndExecuteDataDO.mergeKey, LongtermDemandAndExecuteDataDO.mergeKey,
                (a, b) -> {
                    LongtermDemandAndExecuteDataDO one = ListUtils.isNotEmpty(a) ? a.get(0) : b.get(0);
                    LongtermDemandAndExecuteDataDO result = JSON.clone(one);
                    result.setDemandType("新增");
                    result.setAnualTotalDemandCore(NumberUtils.sum(b, o -> o.getAnualTotalDemandCore()));
                    result.setAnualTotalDemandUnit(NumberUtils.sum(b, o -> o.getAnualTotalDemandUnit()));
                    result.setAnualCumulativeDeliveryCore(NumberUtils.sum(a, o -> o.getAnualCumulativeDeliveryCore()));
                    result.setAnualCumulativeDeliveryUnit(NumberUtils.sum(a, o -> o.getAnualCumulativeDeliveryUnit()));
                    result.setAnualTotalDeliveryCore(NumberUtils.sum(a, o -> o.getAnualTotalDeliveryCore()));
                    result.setAnualTotalDeliveryUnit(NumberUtils.sum(a, o -> o.getAnualTotalDeliveryUnit()));
                    return result;
                });
    }

    private List<LongtermDemandAndExecuteDataDO> trans(String resourceType,
                                                       ResPlanHolidayWeekDO weekDO, List<DeliveryDataDTO> utilWeek, List<DeliveryDataDTO> totalYear) {

        LongtermDemandAndExecuteDataDO data = new LongtermDemandAndExecuteDataDO();
        data.setYear(weekDO.getYear());
        data.setWeekNum(weekDO.getWeek());
        data.setResourceType(resourceType);

        return ListUtils.merge(utilWeek, totalYear,
                DeliveryDataDTO.mergeKey, DeliveryDataDTO.mergeKey, (a, b) -> {
                    DeliveryDataDTO one = ListUtils.isNotEmpty(a) ? a.get(0) : b.get(0);
                    LongtermDemandAndExecuteDataDO tmp = JSON.clone(data);
                    tmp.setQuotaWeek(one.getQuotaWeek());
                    tmp.setBizType(one.getBizType());
                    tmp.setProjectName(one.getProjectName());
                    tmp.setPlanProductName(one.getPlanProductName());
                    tmp.setDept(one.getDept());
                    tmp.setCustomBg(one.getCustomBg());
                    tmp.setMachineType(one.getMachineType());
                    tmp.setMachineTypeFamily(one.getMachineTypeFamily());
                    tmp.setAnualTotalDeliveryUnit(NumberUtils.sum(b, o -> o.getUnit()));
                    tmp.setAnualTotalDeliveryCore(NumberUtils.sum(b, o -> o.getCore()));
                    tmp.setAnualCumulativeDeliveryUnit(NumberUtils.sum(a, o -> o.getUnit()));
                    tmp.setAnualCumulativeDeliveryCore(NumberUtils.sum(a, o -> o.getCore()));
                    return tmp;
                });
    }

    private Map<String, String> getMachineTypeGeneral() {
        List<LongtermDemandAndExecuteMachineTypeDictDO> all = cdLabDbHelper.getAll(LongtermDemandAndExecuteMachineTypeDictDO.class);
        return ListUtils.toMap(all, LongtermDemandAndExecuteMachineTypeDictDO::getMachineTypeFamily, LongtermDemandAndExecuteMachineTypeDictDO::getMachineTypeFamilyGeneral);
    }
}
