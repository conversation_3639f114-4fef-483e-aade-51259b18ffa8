package cloud.demand.lab.modules.longterm.predict.dto.data_split;

import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputArgsDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputInstance2deviceRateDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitDO;
import cloud.demand.lab.modules.longterm.predict.service.impl.SplitServiceImpl.SplitDependDataDTO;
import cloud.demand.lab.modules.longterm.predict.service.impl.SplitServiceImpl.SplitInfo;
import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DeviceTypePurchaseDataSplit extends DataSplitTree<LongtermPredictOutputPurchaseSplitDO> {

    public DeviceTypePurchaseDataSplit(String msg) {
        super(msg);
    }

    // 拆分顺序
    DeviceTypePurchaseDataSplitOrder splitOrder = new DeviceTypePurchaseDataSplitOrder();

    SplitDependDataDTO splitDependDataDTO;

    LongtermPredictInputArgsDO curInputArgs;

    List<LongtermPredictInputInstance2deviceRateDO>  instance2DeviceRate;


    // 实际过了多少个月了
    Integer realMonthLen = 0;
    // 实际发生了的执行量
    BigDecimal realMonthTotalPurchase = BigDecimal.ZERO;
    List<PurchaseRealMonthDetail> realMonthDetail = new ArrayList<>();
    List<PurchaseRealMonthDetail> lastMonthRealPurchaseDetail = new ArrayList<>();

    // 过程数据
    LongtermPredictOutputPurchaseDO outputPurchaseDO;
    public List<Map<String, Map<String, SplitInfo>>> historyList = new ArrayList<>();


    @Data
    public static class PurchaseRealMonthDetail {
        @Column("country_name")
        String countryName;
        @Column("region_name")
        String regionName;
        @Column("core")
        BigDecimal realMonthTotalPurchase = BigDecimal.ZERO;
    }
}
