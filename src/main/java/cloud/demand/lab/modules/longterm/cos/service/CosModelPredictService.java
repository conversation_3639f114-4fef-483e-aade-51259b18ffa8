package cloud.demand.lab.modules.longterm.cos.service;

import cloud.demand.lab.modules.longterm.cos.web.req.QueryAlgorithmPredictResultReq;
import cloud.demand.lab.modules.longterm.cos.web.req.ReCalculateAlgorithmPredictReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryAlgorithmPredictResultResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.ReCalculateAlgorithmPredictResp;

public interface CosModelPredictService {

    /**
     * 查询预测结果，当taskId=0时，有可能从来都没有计算过，此时会触发计算下数据，因此整个系统第一次访问会稍慢
     */
    QueryAlgorithmPredictResultResp queryPredictResult(QueryAlgorithmPredictResultReq req);

    /**
     * 触发预测结果重跑
     */
    ReCalculateAlgorithmPredictResp reCalculateAlgorithmPredict(ReCalculateAlgorithmPredictReq req);

}
