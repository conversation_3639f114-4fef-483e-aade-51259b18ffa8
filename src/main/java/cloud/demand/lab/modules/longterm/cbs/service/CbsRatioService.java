package cloud.demand.lab.modules.longterm.cbs.service;

import cloud.demand.lab.modules.longterm.cbs.dto.CbsDemandMarketDTO;
import cloud.demand.lab.modules.longterm.cbs.dto.CbsScaleFinishAndLatestDTO;
import cloud.demand.lab.modules.longterm.cbs.entity.CbsRatioDetailDO;
import cloud.demand.lab.modules.longterm.cbs.web.req.ratio.*;
import cloud.demand.lab.modules.longterm.cbs.web.resp.ratio.*;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictTaskDO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * CBS配比方案相关接口
 */
public interface CbsRatioService {

    /**
     * 查询配比方案列表
     */
    QueryCbsRatioCategoryResp queryCbsRatioCategory(QueryCbsRatioCategoryReq req);

    /**
     * 创建配比方案
     */
    CreateCbsRatioResp createCbsRatio(CreateCbsRatioReq req);

    /**
     * 查看配比方案详情
     */
    QueryCbsRatioDetailResp queryCbsRatioDetail(QueryCbsRatioDetailReq req);

    /**
     * 删除配比方案
     */
    DeleteCbsRatioResp deleteCbsRatio(DeleteCbsRatioReq req);

    /**
     * 更新配比方案
     */
    UpdateCbsRatioResp updateCbsRatio(UpdateCbsRatioReq req);

    /**
     * 复制配比方案
     */
    CopyCbsRatioResp copyCbsRatio(CopyCbsRatioReq req);

    /**
     * 获取机型配比明细
     * @param categoryId
     * @return
     */
    List<CbsRatioDetailDO> buildCvmInstanceRatioList(Long categoryId);
    /**
     * 查询配比历史趋势图
     */
    QueryCbsRatioHistoryResp queryCbsRatioHistory(QueryCbsRatioHistoryReq req);

    /**
     * 查询cbs机型大类对应的机型族映射
     * @param req
     * @return
     */
    queryInstanceTypeResp queryInstanceType(queryInstanceTypeReq req);

    /**
     * 获取增量除以采购量比例。比例从cvm净增总量和cvm采购总量比例算出（不会细分到机型），用作cbs采购量->净增量的换算
     * @param taskId
     * @param splitversionId
     * @return key是年份+策略类型 value是比例
     */
    Map<String, BigDecimal> getScaleRatios(Long taskId, Long splitversionId);

//    /**
//     * 获取完成量的增量/采购比例（不需要
//     * @param taskId
//     * @param splitVersionId
//     * @return
//     */
//    BigDecimal getFinishRatio(Long taskId, Long splitVersionId);
//
//    /**
//     * 获取最新日期的增量/采购比例（不需要
//     * @param taskId
//     * @param splitVersionId
//     * @return
//     */
//    BigDecimal getLatestRatio(Long taskId, Long splitVersionId);

    /**
     * 获取净增量大框的完成量和最新日期的增量数据
     * @param finishEndDate
     * @param scaleLastYear
     * @return 包含磁盘数，境内外，日期
     */
    List<CbsScaleFinishAndLatestDTO> getScaleFinishAndLatest(String finishEndDate, String scaleLastYear);

    /**
     * 获取采购大框的完成量数据
     * @param taskDO
     * @param params
     * @return 包含境内外，台数，日期
     */
    List<CbsDemandMarketDTO> getFinishedPurchaseData(LongtermPredictTaskDO taskDO, Map<String, Object> params);

    /**
     * 获取采购大框的最新日期采购数据
     * @param taskDO
     * @return 包含境内外，台数，日期
     */
    List<CbsDemandMarketDTO> getLatestPurchaseData(LongtermPredictTaskDO taskDO);

    /**
     * 获取转换过程当中的各种配比
     * @return
     */
    Map<String,BigDecimal> getCommonRatioMap(String taskId);

    /**
     * 设置境内外配比
     * @param req
     * @return
     */
    SetRatioResp setCustomhouseRatio(SetRatioReq req);

//    /**
//     * 设置某年的增益配比
//     * @param req
//     * @return
//     */
//    SetRatioResp updateEnhanceYearRatio(SetRatioReq req);

    /**
     * 获取所有全局配比
     * @param req
     * @return
     */
    GetCommonRatioResp getAllCommonRatios(GetCommonRatioReq req);

    /**
     * 获取境内外+机型增量占总增量的比值，用于重新分配数据
     * 按照三个月滚动增量计算
     * @return
     */
    Map<String,BigDecimal> getRegionAndInstanceTypeIncreaseShareRatio();

    /**
     * 创建某年的增益配比
     * @param req
     * @return
     */
    SetRatioResp createEnhanceYearRatio(SetRatioReq req);

    /**
     * 获取cbs至截止日期的当年已完成增量
     * @param params
     * @return
     */
    BigDecimal queryCbsFinished(Map<String, Object> params);

    /**
     * 获取境外占比
     * @param req
     * @return
     */
    GetCustomhouseRatioResp getCustomhouseRatio(GetCustomhouseRatioReq req);


    BigDecimal getScaleByMonth(int year,int month);

    GetBizRangeRatioResp getBizRangeRatio(GetBizRangeRatioReq req);
}
