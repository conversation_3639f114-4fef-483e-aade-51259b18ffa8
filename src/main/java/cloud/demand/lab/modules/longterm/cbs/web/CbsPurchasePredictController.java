package cloud.demand.lab.modules.longterm.cbs.web;


import cloud.demand.lab.modules.longterm.cbs.service.CbsPurchasePredictService;
import cloud.demand.lab.modules.longterm.cbs.web.req.scale_predict.QueryCbsPurchaseTotalReq;
import cloud.demand.lab.modules.longterm.cbs.web.resp.scale_predict.QueryCbsPurchaseHalfYearAccumulateResp;
import cloud.demand.lab.modules.longterm.cbs.web.resp.scale_predict.QueryCbsPurchaseTotalResp;
import cloud.demand.lab.modules.longterm.predict.service.SplitService;
import cloud.demand.lab.modules.longterm.predict.web.req.split.QuerySplitVersionReq;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.QuerySplitVersionResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;

/**
 * CBS中长期采购量及其预测
 */
@JsonrpcController("/cbs_longterm_predict")
@Slf4j
public class CbsPurchasePredictController {

    @Resource
    private CbsPurchasePredictService cbsPurchasePredictService;

    @Resource
    private SplitService splitService;
    /**
     * 右边两个框，采购量预测
     */
    @RequestMapping
    public QueryCbsPurchaseTotalResp queryCbsPurchaseTotal(@JsonrpcParam QueryCbsPurchaseTotalReq req) {
        QuerySplitVersionReq querySplitVersionReq = new QuerySplitVersionReq();
        if(req.getTaskId()==null)throw new RuntimeException("未指定拆分版本");
        if(req.getRatioCategoryId()==null)throw new RuntimeException("未指定配比版本");
        querySplitVersionReq.setTaskId(req.getTaskId());
        QuerySplitVersionResp querySplitVersionResp = splitService.querySplitVersionList(querySplitVersionReq);
        if(querySplitVersionResp.getSplitVersionList().isEmpty())throw new RuntimeException("未找到拆分版本，请等待任务预测完成后刷新");
        Long splitVersionId = querySplitVersionResp.getSplitVersionList().get(0).getSplitVersionId();
        req.setSplitVersionId(splitVersionId);
        return cbsPurchasePredictService.queryCbsPurchaseTotal(req);
    }

    /**
     * 半年累计预测
     */
    @RequestMapping
    public QueryCbsPurchaseHalfYearAccumulateResp queryCbsPurchaseHalfYearAccumulate(@JsonrpcParam QueryCbsPurchaseTotalReq req) {
        QuerySplitVersionReq querySplitVersionReq = new QuerySplitVersionReq();
        if(req.getTaskId()==null)throw new RuntimeException("未指定拆分版本");
        if(req.getRatioCategoryId()==null)throw new RuntimeException("未指定配比版本");
        querySplitVersionReq.setTaskId(req.getTaskId());
        QuerySplitVersionResp querySplitVersionResp = splitService.querySplitVersionList(querySplitVersionReq);
        if(querySplitVersionResp.getSplitVersionList().isEmpty())throw new RuntimeException("未找到拆分版本，请等待任务预测完成后刷新");
        Long splitVersionId = querySplitVersionResp.getSplitVersionList().get(0).getSplitVersionId();
        req.setSplitVersionId(splitVersionId);
        return cbsPurchasePredictService.queryCbsPurchaseHalfYearAccumulate(req);
    }
}
