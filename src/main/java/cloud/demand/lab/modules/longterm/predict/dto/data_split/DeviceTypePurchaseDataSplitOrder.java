package cloud.demand.lab.modules.longterm.predict.dto.data_split;

import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputScaleDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitIndustryDeptDO;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

public class DeviceTypePurchaseDataSplitOrder implements DataSplitOrder {

    int i = -1;
    private static final List<Function<LongtermPredictInputScaleIncreaseDTO, String>> splitKey = new ArrayList<>();
    private static final List<Function<LongtermPredictOutputPurchaseSplitDO, String>> historySplitKey = new ArrayList<>();
    private static final List<BiConsumer<LongtermPredictOutputPurchaseSplitDO, String>> splitKeySetter = new ArrayList<>();
    private static final List<String> splitName = new ArrayList<>();

    public static String CUSTOM_TITLE_HOUSE_SPLIT = "境内外";
    public static String DEVICE_SPLIT = "拆分物理机类型";
    public static String MONTH_SPLIT = "拆分年月";
    public static String CUSTOMER_SPLIT = "客户类型拆分";
    public static String INSTANCE_FAMILY_SPLIT = "拆分机型大类";
    public static String ZONE_NAME_SPLIT = "拆分可用区";

    static {
        splitKey.add(LongtermPredictInputScaleIncreaseDTO::getCustomhouseTitle);
        splitKey.add((o) -> "monthSplit");
        splitKey.add((o) -> "customerSplit");
        splitKey.add(LongtermPredictInputScaleIncreaseDTO::getInstanceFamily);
        splitKey.add(LongtermPredictInputScaleIncreaseDTO::getZoneName);
        splitKey.add((o) -> "deviceSplit");

        historySplitKey.add(LongtermPredictOutputPurchaseSplitDO::getCustomhouseTitle);
        historySplitKey.add((o)-> "monthSplit");
        historySplitKey.add((o)-> "customerSplit");
        historySplitKey.add(LongtermPredictOutputPurchaseSplitDO::getInstanceFamily);
        historySplitKey.add(LongtermPredictOutputPurchaseSplitDO::getZoneName);
        historySplitKey.add((o)-> "deviceSplit");

        splitKeySetter.add(LongtermPredictOutputPurchaseSplitDO::setCustomhouseTitle);
        splitKeySetter.add(LongtermPredictOutputPurchaseSplitDO::setYearMonthStr);
        splitKeySetter.add(LongtermPredictOutputPurchaseSplitDO::setCustomerType);
        splitKeySetter.add(LongtermPredictOutputPurchaseSplitDO::setInstanceFamily);
        splitKeySetter.add(LongtermPredictOutputPurchaseSplitDO::setZoneName);
        splitKeySetter.add(LongtermPredictOutputPurchaseSplitDO::setDeviceType);

        splitName.add(CUSTOM_TITLE_HOUSE_SPLIT);
        splitName.add(MONTH_SPLIT);
        splitName.add(CUSTOMER_SPLIT);
        splitName.add(INSTANCE_FAMILY_SPLIT);
        splitName.add(ZONE_NAME_SPLIT);
        splitName.add(DEVICE_SPLIT);
    }

    @Override
    public String getSplitPrefixKey(Object data) {
        if (data instanceof LongtermPredictOutputPurchaseSplitDO) {
            return "total" +
                    historySplitKey.subList(0, i).stream()
                            .map(func -> func.apply((LongtermPredictOutputPurchaseSplitDO) data))
                            .collect(Collectors.joining("@"));
        } else if (data instanceof LongtermPredictInputScaleIncreaseDTO) {
            return "total" +
                    splitKey.subList(0, i).stream()
                            .map(func -> func.apply((LongtermPredictInputScaleIncreaseDTO) data))
                            .collect(Collectors.joining("@"));
        } else {
            throw new RuntimeException("data type error");
        }
    }

    @Override
    public String getSplitCurKey(Object data) {
        if (data instanceof LongtermPredictOutputPurchaseSplitDO) {
            return historySplitKey.get(i).apply((LongtermPredictOutputPurchaseSplitDO) data);
        } else if (data instanceof LongtermPredictInputScaleIncreaseDTO) {
            return splitKey.get(i).apply((LongtermPredictInputScaleIncreaseDTO) data);
        } else {
            throw new RuntimeException("data type error");
        }
    }

    @Override
    public void setSplitCurKey(Object data, String key) {
        splitKeySetter.get(i).accept((LongtermPredictOutputPurchaseSplitDO) data, key);
    }

    @Override
    public String getName() {
        return splitName.get(i);
    }

    @Override
    public boolean hasNext() {
        return i < splitName.size() - 1;
    }

    @Override
    public DataSplitOrder next() {
        i++;
        return this;
    }
}
