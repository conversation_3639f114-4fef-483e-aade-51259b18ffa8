package cloud.demand.lab.modules.longterm.cos.job;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictTaskDO;
import cloud.demand.lab.modules.longterm.cos.enums.Constants;
import cloud.demand.lab.modules.longterm.cos.service.CosCreatePredictTaskService;
import cloud.demand.lab.modules.longterm.predict.enums.LongtermPredictTaskStatusEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.RedisMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 负责COS中长期模型预测的任务
 */
@Slf4j
@Service
public class CosLongtermPredictTask {

    @Resource
    RedisHelper redisHelper;
    @Resource
    private DBHelper cdLabDbHelper;
    @Resource
    private CosCreatePredictTaskService cosCreatePredictTaskService;

    /**
     * 处理来自消息队列中的任务。这个可以保证任务实时被处理。
     * 为什么用定时任务，因为生产的任务节点和web节点分来，确保任务执行异常不影响web节点。
     * 说明，这里会长期占用schedule池中的一条线程，schedule的线程数已由20调整为30，故没有影响。
     */
    @Scheduled(fixedDelay = 1L)
    public void fromRedisMq() {
        RedisMsg msg = redisHelper.receive(Constants.REDIS_QUEUE_LONGTERM_PREDICT_TASK_COS);
        if (msg != null) {
            Long taskId = NumberUtils.parseLong(msg.getMsg());
            try {
                if (taskId != null) {
                    CosLongtermPredictTaskDO taskDO = cdLabDbHelper.getByKey(CosLongtermPredictTaskDO.class, taskId);
                    if (taskDO == null) {
                        log.error("cos task id {} not exist, ignore task", taskId);
                        return;
                    }
                    cosCreatePredictTaskService.doRunPredictTask(taskId);
                }
            } catch (Exception e) {
                log.error("run cos longterm predict task fail, taskId:{}", taskId, e);
            } finally {
                redisHelper.ack(Constants.REDIS_QUEUE_LONGTERM_PREDICT_TASK_COS, msg.getUuid()); // 只处理一次
            }
        }
    }

    /**
     * 定期从数据库里扫描进行处理
     */
    @Scheduled(fixedDelay = 30000)
    public void fromDB() {
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("task_status = ?", LongtermPredictTaskStatusEnum.NEW.getCode());

        List<CosLongtermPredictTaskDO> toRunTask = cdLabDbHelper.getAll(CosLongtermPredictTaskDO.class,
                whereSQL.getSQL(), whereSQL.getParams());
        for (CosLongtermPredictTaskDO task : toRunTask) {
            try {
                cosCreatePredictTaskService.doRunPredictTask(task.getId());
            } catch (Exception e) {
                log.error("run cos longterm predict task fail, taskId:{}", task.getId(), e); // 不影响其他任务执行
            }
        }
    }

}
