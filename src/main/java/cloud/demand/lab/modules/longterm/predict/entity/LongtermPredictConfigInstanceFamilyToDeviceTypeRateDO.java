package cloud.demand.lab.modules.longterm.predict.entity;

import cloud.demand.lab.common.config.DBList;
import cloud.demand.lab.common.config.DBList.DBHelperWithClz;
import cloud.demand.lab.common.entity.BaseDO;
import cloud.demand.lab.modules.longterm.predict.entity.SplitRateKeyGetters.RateGetter;
import cloud.demand.lab.modules.longterm.predict.entity.SplitRateKeyGetters.SplitDeviceTypeKeyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pugwoo.wooutils.json.JSON;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import org.nutz.lang.Lang;

@Data
@ToString
@Table("longterm_predict_config_instance_family_to_device_type_rate")
public class LongtermPredictConfigInstanceFamilyToDeviceTypeRateDO extends BaseDO
        implements SplitDeviceTypeKeyGetter {

    public static DBHelperWithClz<LongtermPredictConfigInstanceFamilyRateDO> db() {
        return DBHelperWithClz.create(DBList.cdLabDbHelper);
    }

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /**
     * 机型大类<br/>Column: [instance_family]
     */
    @Column(value = "instance_family")
    private String instanceFamily;

    /**
     * 物理机机型<br/>Column: [device_type]
     */
    @Column(value = "device_type")
    private String deviceType;

    /**
     * 占比<br/>Column: [rate]
     */
    @Column(value = "rate")
    private BigDecimal rate;

    @JsonIgnore
    @Column(value = "year_month_item")
    private String yearMonthItem;

    List<YearMonthItem> yearMonthItemList = new ArrayList<>();

    public void parseYearMonthItemFromDb() {
        yearMonthItemList = JSON.parseToList(yearMonthItem, YearMonthItem.class);
        if (yearMonthItemList == null) {
            yearMonthItemList = new ArrayList<>();
        }
    }

    public List<YearMonthItem> getYearMonthItemList() {
        parseYearMonthItemFromDb();
        return yearMonthItemList;
    }


    @Data
    public static class YearMonthItem implements RateGetter {

        private Integer year;
        private Integer month;
        private BigDecimal rate;

        @JsonIgnore
        public YearMonth getYm() {
            return YearMonth.of(year, month);
        }
    }

}