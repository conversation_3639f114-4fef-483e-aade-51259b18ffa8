package cloud.demand.lab.modules.longterm.cbs.web.resp.scale_predict;

import cloud.demand.lab.modules.longterm.cbs.dto.CbsPurchaseDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class QueryCbsPurchaseTotalResp {
    private List<CbsYearItem> cbsYearItems;

    private List<CbsPurchaseDTO> cbsPurchaseDTOList;
    @Data
    public static class CbsYearItem{
        //该类内展示的数据年
        private int year;
        private List<CbsStrategyItem> cbsStrategyItems;
    }


    @Data
    public static class CbsStrategyItem{
        //策略类型
        private String strategyType;

        //新计算方法：当年cvm增量*配比值
        //当年净增规模预测
        private BigDecimal PurchasePredictTotal;
        //境内
        private BigDecimal PurchasePredictDomestic;
        //境外
        private BigDecimal PurchasePredictAboard;


        //实际已完成，次年不展示
        private BigDecimal finishedTotal;
        //进度
        private BigDecimal finishedRate;
        //截止日期
        private String finishedClosingDate;
        //境内
        private BigDecimal finishedDomestic;
        //境外
        private BigDecimal finishedAboard;

        // ======================== 最新日期的

        //实际已完成，次年不展示
        private BigDecimal latestFinishedTotal;
        //进度
        private BigDecimal latestFinishedRate;
        //截止日期
        private String latestClosingDate;
//        //内部
//        private BigDecimal latestFinishedInner;
//        //外部
//        private BigDecimal latestFinishedOuter;
        //境内
        private BigDecimal latestFinishedDomestic;
        //境外
        private BigDecimal latestFinishedAboard;
    }
}
