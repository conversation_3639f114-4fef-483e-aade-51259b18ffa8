package cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;


/**
 * 总结框1返回体
 */
@Data
public class PurchasePredictHalfYearAccumulateResp {


    List<StrategyItem> strategyItemList = new ArrayList<>();

    @Data
    public static class StrategyItem {
        //策略类型
        private String strategyType;
        private String strategyTypeName;
        private List<HalfYearAccumulateItem> halfYearAccumulateItems = new ArrayList<>();
    }

    @Data
    public static class HalfYearAccumulateItem {

        private String startDate;
        private String endDate;
        //当年净增规模预测
        private BigDecimal purchasePredictAccumulate;
    }






}
