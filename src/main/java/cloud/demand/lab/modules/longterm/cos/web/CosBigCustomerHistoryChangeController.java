package cloud.demand.lab.modules.longterm.cos.web;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.modules.longterm.cos.service.CosBigCustomerHistoryChangeService;
import cloud.demand.lab.modules.longterm.cos.web.dto.BigCustomerHistoryChangeDTO;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryBigCustomerHistoryChangeReq;
import cloud.demand.lab.modules.longterm.cos.web.req.SaveBigCustomerHistoryChangeReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryBigCustomerHistoryChangeResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.SaveBigCustomerHistoryChangeResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;

/**
 * 负责大客户历史变动数据增删改查
 */
@JsonrpcController("/cos-longterm-predict")
@Slf4j
public class CosBigCustomerHistoryChangeController {

    @Resource
    private CosBigCustomerHistoryChangeService cosBigCustomerHistoryChangeService;

    /**
     * 查询大客户历史变动数据
     * @param req 查询请求，必须包含categoryId，taskId可选（为0时查询默认数据）
     * @return 大客户历史变动数据列表
     */
    @RequestMapping
    public QueryBigCustomerHistoryChangeResp queryBigCustomerHistoryChange(@JsonrpcParam QueryBigCustomerHistoryChangeReq req) {
        if (req == null || req.getCategoryId() == null) {
            throw new WrongWebParameterException("方案id(categoryId)不能为空");
        }
        if (req.getTaskId() == null) {
            req.setTaskId(0L);
        }

        return cosBigCustomerHistoryChangeService.queryBigCustomerHistoryChange(req);
    }

    /**
     * 保存大客户历史变动数据
     * 采用覆盖策略：先删除已存在的数据，再全量插入新数据
     * @param req 保存请求，必须包含categoryId，taskId可选（未传时填0）
     * @return 保存结果
     */
    @RequestMapping
    public SaveBigCustomerHistoryChangeResp saveBigCustomerHistoryChange(@JsonrpcParam SaveBigCustomerHistoryChangeReq req) {
        if (req == null || req.getCategoryId() == null) {
            throw new WrongWebParameterException("方案id(categoryId)不能为空");
        }
        if (req.getDataList() == null) {
            throw new WrongWebParameterException("数据列表不能传null，如确实要删除全部数据，请传递空数组");
        }
        if (req.getAutoReCalPredict() == null) {
            req.setAutoReCalPredict(true);
        }

        for (BigCustomerHistoryChangeDTO data : req.getDataList()) {
            if (data.getIsOutCustomer() == null) {
                throw new WrongWebParameterException("大客户" + data.getCustomerName() + "的内外部标识不能为空");
            }
            if (data.getStartDate() == null) {
                throw new WrongWebParameterException("大客户" + data.getCustomerName() + "的开始时间不能为空");
            }
            if (data.getEndDate() == null) {
                throw new WrongWebParameterException("大客户" + data.getCustomerName() + "的结束时间不能为空");
            }
            if (data.getNetChange() == null) {
                throw new WrongWebParameterException("大客户" + data.getCustomerName() + "的变化量不能为空");
            }
            if (data.getEndDate().isBefore(data.getStartDate())) {
                throw new WrongWebParameterException("大客户" + data.getCustomerName() + "的结束时间不能早于开始时间");
            }
        }

        return cosBigCustomerHistoryChangeService.saveBigCustomerHistoryChange(req);
    }
}
