package cloud.demand.lab.modules.longterm.predict.dto.history_and_predict_view;

import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputSplitVersionDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictTaskDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
public class PredictHistoryYearMonthVO extends LongtermPredictTaskDO {


    @RelatedColumn(localColumn = "id",remoteColumn = "task_id")
    private List<LongtermPredictOutputSplitVersionDO> longtermPredictOutputSplitVersionDOs;

}
