package cloud.demand.lab.modules.longterm.cbs.web.resp.scale_predict;

import cloud.demand.lab.modules.longterm.cbs.dto.CbsLongtermPredictScaleDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class QueryCbsScalePredictTotalResp {

    private List<CbsYearItem> cbsYearItems=new ArrayList<>();


    // 新增字段
    private List<CbsLongtermPredictScaleDTO> cbsIncreaseDTOList=new ArrayList<>();

    @Data
    public static class CbsYearItem{
        //该类内展示的数据年
        private int year;
        private List<CbsStrategyItem> cbsStrategyItems=new ArrayList<>();
    }


    @Data
    public static class CbsStrategyItem{
        //策略类型
        private String strategyType;

        //新计算方法：当年cvm增量*配比值
        //当年净增规模预测
        private BigDecimal increasePredictTotal;
//        //内部
//        private BigDecimal increasePredictInner;
//        //外部
//        private BigDecimal increasePredictOuter;
        //境内
        private BigDecimal increasePredictDomestic;
        //境外
        private BigDecimal increasePredictAboard;
//
//        private BigDecimal increasePredictTotalCore;


        //实际已完成，次年不展示
        private BigDecimal finishedTotal;
        //进度
        private BigDecimal finishedRate;
        //截止日期
        private String finishedClosingDate;
//        //内部
//        private BigDecimal finishedInner;
//        //外部
//        private BigDecimal finishedOuter;
        //境内
        private BigDecimal finishedDomestic;
        //境外
        private BigDecimal finishedAboard;

        // ======================== 最新日期的

        //实际已完成，次年不展示
        private BigDecimal latestFinishedTotal;
        //进度
        private BigDecimal latestFinishedRate;
        //截止日期
        private String latestClosingDate;
//        //内部
//        private BigDecimal latestFinishedInner;
//        //外部
//        private BigDecimal latestFinishedOuter;
        //境内
        private BigDecimal latestFinishedDomestic;
        //境外
        private BigDecimal latestFinishedAboard;
    }
}
