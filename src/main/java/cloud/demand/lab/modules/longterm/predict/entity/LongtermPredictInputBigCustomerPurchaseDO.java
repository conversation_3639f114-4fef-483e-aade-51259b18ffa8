package cloud.demand.lab.modules.longterm.predict.entity;

import cloud.demand.lab.common.entity.BaseDO;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("longterm_predict_input_big_customer_purchase")
public class LongtermPredictInputBigCustomerPurchaseDO extends BaseDO {

    @Column(value = "task_id")
    private Long taskId;

    @Column(value = "split_version_id")
    private Long splitVersionId;

    /** 客户名称<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 可用区名称<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "year")
    private Integer year;

    @Column(value = "year_month_str")
    private String yearMonthStr;

    @Column(value = "quarter")
    private Integer quarter;

    @Column(value = "half_year")
    private Integer halfYear;

    /** 物理机设备核心数<br/>Column: [purchase_core] */
    @Column(value = "purchase_core")
    private Integer purchaseCore;

}