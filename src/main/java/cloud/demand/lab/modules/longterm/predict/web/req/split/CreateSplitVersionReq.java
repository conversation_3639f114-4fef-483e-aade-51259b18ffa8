package cloud.demand.lab.modules.longterm.predict.web.req.split;

import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.BizRangeTypeRate;
import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.CustomhouseTitleRate;
import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.InstanceFamilyRate;
import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.InstanceFamilyToDeviceTypeRate;
import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.InstanceFamilyToZoneNameRate;
import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.MonthRate;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.Instance2DeviceRateResp;
import java.util.List;
import lombok.Data;

@Data
public class CreateSplitVersionReq {

    private Long taskId;
    private String name;

    private String note;

    // 大客户的预测
    // 这个数据拆分的时候才保存，每次拆分可能不一样
    List<BigCustomerExcelDTO> bigCustomerForecast;

    // 一共6份配置表
    List<CustomhouseTitleRate> customhouseTitleRate;
    List<BizRangeTypeRate> bizRangeTypeRate;
    List<InstanceFamilyRate> instanceFamilyRate;
    List<InstanceFamilyToDeviceTypeRate> instanceFamilyToDeviceTypeRate;
    List<InstanceFamilyToZoneNameRate> instanceFamilyToZoneNameRate;
    List<MonthRate> monthRate;

    /**
     * 修改的比例，有效字段为 ID 和 rate， 不会改变 db 中的rate
     */
    private  List<Instance2DeviceRateResp>  instance2DeviceRate;

}
