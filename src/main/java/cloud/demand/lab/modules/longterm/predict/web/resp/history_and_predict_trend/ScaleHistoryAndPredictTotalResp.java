package cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


/**
 * 总结框1返回体
 */
@Data
public class ScaleHistoryAndPredictTotalResp {

    private List<yearItem> yearItems;

    private String message;



    @Data
    public static class yearItem{
        //年
        private int year;

        List<StrategyItem> StrategyItems;
    }

    @Data
    public static class StrategyItem {
        //策略类型
        private String strategyType;

        //当年净增规模预测
        private BigDecimal increasePredictTotal;
        //内部
        private BigDecimal increasePredictInner;
        //外部
        private BigDecimal increasePredictOuter;
        //境内
        private BigDecimal increasePredictDomestic;
        //境外
        private BigDecimal increasePredictAboard;


        //实际已完成，次年不展示
        private BigDecimal finished;
        //进度
        private BigDecimal finishedRate;
        //截止日期
        private String closingDate;
        //内部
        private BigDecimal finishedInner;
        //外部
        private BigDecimal finishedOuter;
        //境内
        private BigDecimal finishedDomestic;
        //境外
        private BigDecimal finishedAboard;

        // ======================== 最新日期的

        //实际已完成，次年不展示
        private BigDecimal latestFinished;
        //进度
        private BigDecimal latestFinishedRate;
        //截止日期
        private String latestClosingDate;
        //内部
        private BigDecimal latestFinishedInner;
        //外部
        private BigDecimal latestFinishedOuter;
        //境内
        private BigDecimal latestFinishedDomestic;
        //境外
        private BigDecimal latestFinishedAboard;

    }

    public ScaleHistoryAndPredictTotalResp() {
    }

    public ScaleHistoryAndPredictTotalResp(String message) {
        this.message = message;
    }

}
