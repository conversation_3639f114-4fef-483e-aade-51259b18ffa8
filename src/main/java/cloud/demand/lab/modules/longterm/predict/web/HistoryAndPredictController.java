package cloud.demand.lab.modules.longterm.predict.web;

import cloud.demand.lab.modules.longterm.predict.service.HistoryAndPredictTrendService;
import cloud.demand.lab.modules.longterm.predict.web.req.TaskIdAndSplitIdReq;
import cloud.demand.lab.modules.longterm.predict.web.req.history_and_predict_trend.*;
import cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;

/**
 * 存量历史及预测图
 */
@JsonrpcController("/longterm-predict")
@Slf4j
public class HistoryAndPredictController {

    @Resource
    private HistoryAndPredictTrendService scaleHistoryAndPredictService;


    /**
     * 总结框左侧2
     */
    @RequestMapping
    public ScaleHistoryAndPredictTotalResp scaleHistoryAndPredictTotal(
            @JsonrpcParam ScaleHistoryAndPredictTotalReq req) {
        String validateId = scaleHistoryAndPredictService.validateId(req.getTaskId(), req.getSplitVersionId());
        if (!"SUCCESS".equals(validateId)) {
            return new ScaleHistoryAndPredictTotalResp(validateId);
        } else {
            return scaleHistoryAndPredictService.scaleHistoryAndPredictTotal(req);
        }
    }

    /**
     * 总结框右侧2
     */
    @RequestMapping
    public PurchaseHistoryAndPredictTotalResp purchaseHistoryAndPredictTotal(
            @JsonrpcParam TaskIdAndSplitIdReq req) {
        return scaleHistoryAndPredictService.purchaseHistoryAndPredictTotal(req);
    }

    @RequestMapping
    public PurchasePredictHalfYearAccumulateResp purchasePredictHalfYearAccumulate(
            @JsonrpcParam TaskIdAndSplitIdReq req) {
        return scaleHistoryAndPredictService.purchasePredictHalfYearAccumulate(req);
    }


    /**
     * 存量字典
     */
    @RequestMapping
    public ScaleHistoryAndPredictDictResp scaleHistoryAndPredictDict(@JsonrpcParam LongtermTaskIdReq req) {
        String validateId = scaleHistoryAndPredictService.validateId(req.getTaskId());
        if (!"SUCCESS".equals(validateId)) {
            return new ScaleHistoryAndPredictDictResp(validateId);
        }
        return scaleHistoryAndPredictService.getScaleHistoryAndPredictDict(req);
    }

    /**
     * 采购量字典
     */
    @RequestMapping
    public PurchaseHistoryAndPredictDictResp getPurchaseHistoryAndPredictDict(@JsonrpcParam LongtermTaskIdReq req) {
        return scaleHistoryAndPredictService.getPurchaseHistoryAndPredictDict(req);
    }

    /**
     * 采购量历史趋势图
     */
    @RequestMapping
    public PurchaseHistoryTrendViewResp getPurchaseHistoryTrendView(@JsonrpcParam PurchaseTrendViewReq req) {
        return scaleHistoryAndPredictService.getPurchaseHistoryTrendView(req);
    }

    /**
     * 采购量预测趋势图
     */
    @RequestMapping
    public PurchasePredictTrendViewResp getPurchasePredictTrendView(@JsonrpcParam PurchaseTrendViewReq req) {
        return scaleHistoryAndPredictService.getPurchasePredictTrendView(req);
    }

    /**
     * 存量核心数曲线返回
     */
    @RequestMapping
    public ScaleHistoryResp scaleHistory(@JsonrpcParam ScaleHistoryReq req) {
        String validateId = scaleHistoryAndPredictService.validateId(req.getTaskId(), req.getDims());
        if (!"SUCCESS".equals(validateId)) {
            return new ScaleHistoryResp(validateId);
        }
        return scaleHistoryAndPredictService.getScaleHistory(req);
    }

    /**
     * 添加预测部分曲线
     */
    @RequestMapping
    public ScaleHistoryPredictResp scaleHistoryPredict(@JsonrpcParam ScaleHistoryPredictReq req) {
        String validateId = scaleHistoryAndPredictService.validateId(req.getTaskId(), req.getSplitVersionId(),
                req.getDims());
        if (!"SUCCESS".equals(validateId)) {
            return new ScaleHistoryPredictResp(validateId);
        }
        return scaleHistoryAndPredictService.scaleHistoryPredict(req);
    }

    /**
     * 枚举预测部分添加的年月
     */
    @RequestMapping
    public ScaleHistoryAndPredictYearMonthResp getScaleHistoryAndPredictYearMonth(
            @JsonrpcParam ScaleHistoryAndPredictYearMonthReq req) {
        String validateId = scaleHistoryAndPredictService.validateCategoryId(req.getTaskId(), req.getCategoryId());
        if (!"SUCCESS".equals(validateId)) {
            return new ScaleHistoryAndPredictYearMonthResp(validateId);
        } else {
            return scaleHistoryAndPredictService.getScaleHistoryAndPredictYearMonth(req);
        }
    }

    @RequestMapping
    public PurchaseGlobalTrendResp getPurchaseGlobalTrend(@JsonrpcParam PurchaseGlobalTrendReq req) {
        return scaleHistoryAndPredictService.getPurchaseGlobalTrend(req);
    }

}
