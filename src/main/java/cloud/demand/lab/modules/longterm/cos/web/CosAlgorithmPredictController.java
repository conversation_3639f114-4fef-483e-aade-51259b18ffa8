package cloud.demand.lab.modules.longterm.cos.web;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.modules.longterm.cos.service.CosAlgorithmPredictService;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryAlgorithmPredictResultReq;
import cloud.demand.lab.modules.longterm.cos.web.req.ReCalculateAlgorithmPredictReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryAlgorithmPredictResultResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.ReCalculateAlgorithmPredictResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;

/**
 * 负责cos中长期算法预测
 */
@JsonrpcController("/cos-longterm-predict")
@Slf4j
public class CosAlgorithmPredictController {

    @Resource
    private CosAlgorithmPredictService cosAlgorithmPredictService;

    /**
     * 查询预测结果，当taskId=0时，有可能从来都没有计算过，此时会触发计算下数据，因此整个系统第一次访问会稍慢
     */
    @RequestMapping
    public QueryAlgorithmPredictResultResp queryAlgorithmPredictResult(@JsonrpcParam QueryAlgorithmPredictResultReq req) {
        if (req == null || req.getCategoryId() == null) {
            throw new WrongWebParameterException("方案id(categoryId)不能为空");
        }
        if (req.getTaskId() == null) {
            req.setTaskId(0L);
        }

        return cosAlgorithmPredictService.queryPredictResult(req);
    }

    /**
     * 触发预测结果重跑
     */
    @RequestMapping
    public ReCalculateAlgorithmPredictResp reCalculateAlgorithmPredict(@JsonrpcParam ReCalculateAlgorithmPredictReq req) {
        if (req == null || req.getCategoryId() == null) {
            throw new WrongWebParameterException("方案id(categoryId)不能为空");
        }
        if (req.getTaskId() == null) {
            req.setTaskId(0L);
        }

        return cosAlgorithmPredictService.reCalculateAlgorithmPredict(req);
    }

}
