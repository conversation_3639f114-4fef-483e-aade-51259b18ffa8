package cloud.demand.lab.modules.longterm.cos.web;

import cloud.demand.lab.modules.longterm.cos.service.CosCreatePredictTaskService;
import cloud.demand.lab.modules.longterm.cos.web.req.CreatePredictTaskReq;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryCategoryForCreateReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.CreatePredictTaskResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryCategoryForCreateResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;

/**
 * 负责cos中长期预测
 */
@JsonrpcController("/cos-longterm-predict")
@Slf4j
public class CosCreatePredictTaskController {

    @Resource
    private CosCreatePredictTaskService cosCreatePredictTaskService;

    /**
     * 查询用于创建任务的方案列表及方案关键信息，【创建预测】弹框使用
     */
    @RequestMapping
    public QueryCategoryForCreateResp queryCategoryForCreate(@JsonrpcParam QueryCategoryForCreateReq req) {
        return cosCreatePredictTaskService.queryCategoryForCreate(req);
    }

    /**
     * 创建预测任务
     */
    @RequestMapping
    public CreatePredictTaskResp createPredictTask(@JsonrpcParam CreatePredictTaskReq req) {
        return cosCreatePredictTaskService.createPredictTask(req);
    }

    // TODO queryLastInputArgs

}
