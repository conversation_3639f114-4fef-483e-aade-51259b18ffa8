package cloud.demand.lab.modules.longterm.predict.service;

import cloud.demand.lab.modules.longterm.predict.web.req.TaskIdAndSplitIdReq;
import cloud.demand.lab.modules.longterm.predict.web.req.history_and_predict_trend.LongtermTaskIdReq;
import cloud.demand.lab.modules.longterm.predict.web.req.history_and_predict_trend.PurchaseGlobalTrendReq;
import cloud.demand.lab.modules.longterm.predict.web.req.history_and_predict_trend.PurchaseTrendViewReq;
import cloud.demand.lab.modules.longterm.predict.web.req.history_and_predict_trend.ScaleHistoryAndPredictTotalReq;
import cloud.demand.lab.modules.longterm.predict.web.req.history_and_predict_trend.ScaleHistoryAndPredictYearMonthReq;
import cloud.demand.lab.modules.longterm.predict.web.req.history_and_predict_trend.ScaleHistoryPredictReq;
import cloud.demand.lab.modules.longterm.predict.web.req.history_and_predict_trend.ScaleHistoryReq;
import cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend.PurchaseGlobalTrendResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend.PurchaseHistoryAndPredictDictResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend.PurchaseHistoryAndPredictTotalResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend.PurchaseHistoryTrendViewResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend.PurchasePredictHalfYearAccumulateResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend.PurchasePredictTrendViewResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend.ScaleHistoryAndPredictDictResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend.ScaleHistoryAndPredictTotalResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend.ScaleHistoryAndPredictYearMonthResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend.ScaleHistoryPredictResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend.ScaleHistoryResp;

public interface HistoryAndPredictTrendService {


    /**
     * 总结框1
     * @param req
     * @return
     */
    ScaleHistoryAndPredictTotalResp scaleHistoryAndPredictTotal(ScaleHistoryAndPredictTotalReq req);


    /**
     * 查询汇总的采购量圆框
     * @param req req
     * @return resp
     */
    PurchaseHistoryAndPredictTotalResp  purchaseHistoryAndPredictTotal(TaskIdAndSplitIdReq req);

    /**
     * 获取字典
     * @param req req
     * @return resp
     */
    ScaleHistoryAndPredictDictResp getScaleHistoryAndPredictDict(LongtermTaskIdReq req);

    /**
     * 获取采购的字典
     * @param req req
     * @return resp
     */
    PurchaseHistoryAndPredictDictResp getPurchaseHistoryAndPredictDict(LongtermTaskIdReq req);

    /**
     * 查询采购量的历史趋势
     * @param req req
     * @return ret
     */
    PurchaseHistoryTrendViewResp getPurchaseHistoryTrendView(PurchaseTrendViewReq req);

    /**
     * 查询采购量预测趋势
     * @param req req
     * @return ret
     */
    PurchasePredictTrendViewResp getPurchasePredictTrendView(PurchaseTrendViewReq req);

    /**
     * 获取历史存量数据曲线
     * @param req
     * @return
     */
    ScaleHistoryResp getScaleHistory(ScaleHistoryReq req);

    /**
     * 添加预测存量数据曲线
     * @param req
     * @return
     */
    ScaleHistoryPredictResp scaleHistoryPredict(ScaleHistoryPredictReq req);

    /**
     * 枚举可添加的预测年月
     * @param req
     * @return
     */
    ScaleHistoryAndPredictYearMonthResp getScaleHistoryAndPredictYearMonth(ScaleHistoryAndPredictYearMonthReq req);

    /**
     * 验证函数
     */
    String validateId(Long taskId);
    String validateId(Long taskId,Long splitVersionId);
    String validateId(Long taskId,Long splitVersionId,String dim);
    String validateId(Long taskId,String dim);
    String validateCategoryId(Long taskId,Long categoryId);

    /**
     *  查询3个滚动半年累积量
     * @param req req
     * @return resp
     */
    PurchasePredictHalfYearAccumulateResp purchasePredictHalfYearAccumulate(TaskIdAndSplitIdReq req);

    /**
     * 查询年度的采购趋势图
     */
    PurchaseGlobalTrendResp getPurchaseGlobalTrend(PurchaseGlobalTrendReq req);

}
