package cloud.demand.lab.modules.longterm.cos.web.resp;

import lombok.Data;

import java.util.List;

@Data
public class QueryCategoryAndTaskListResp {

    private List<Category> categoryList;

    @Data
    public static class Category {

        private Long categoryId;

        private String categoryName;

        /**属于模型预测的不分*/
        private String modelPart;

        /**预测任务列表，时间逆序*/
        private List<Task> predictTaskList;

    }

    @Data
    public static class Task {

        private Long taskId;

        private String yearMonth;

        /**see cloud.demand.lab.modules.longterm.predict.enums.LongtermPredictTaskStatusEnum*/
        private String taskStatusCode;
        /**see cloud.demand.lab.modules.longterm.predict.enums.LongtermPredictTaskStatusEnum*/
        private String taskStatusName;

    }

}
