package cloud.demand.lab.modules.longterm.cos.web.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Data
public class QueryAlgorithmPredictResultResp {

    /**
     * 预测结果信息，以线数据为单位
     */
    private List<Line> lines;

    @Data
    public static class Line {
        /**
         * 范围：全部、内部、外部 3种
         */
        private String scope;
        /**
         * 数据类型：HISTORY、PREDICT 2种，表示历史数据还是预测数据
         */
        private String type;

        /**
         * 当数据是PREDICT时，表示预测所用的算法
         */
        private String algorithm = "";

        /**
         * 数据格式[['yyyy-MM-dd', value],['yyyy-MM-dd', value]]
         */
        private List<List<Object>> points;

        /**当数据是PREDICT时，算法的相关信息*/
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Map<String, Object> algorithmParams;

        /**
         * 当数据是HISTORY时，提供截止预测数据最近半年的增速。
         * 当数据是PREDICT时，按上半年、下半年为周期提供增速。
         */
        private List<IncreaseInfo> increaseInfos;
    }

    /**增速信息*/
    @Data
    public static class IncreaseInfo {
        /**日期名称*/
        private String dateName;
        /**开始日期*/
        private LocalDate startDate;
        /**结束日期*/
        private LocalDate endDate;
        /**增速，注意不是百分比，0.14则表示14%*/
        private BigDecimal rate;
    }

}
