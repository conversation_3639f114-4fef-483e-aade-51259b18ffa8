package cloud.demand.lab.modules.operation_view.operation_view.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("dwd_api_success_data_df")
public class DwsApiSuccessDataDfLocalDO {

    /** 统计日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private String statTime;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 实例大类<br/>Column: [instance_family] */
    @Column(value = "instance_family")
    private String instanceFamily;

    /** 实例规格<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 成功核数<br/>Column: [succ_num] */
    @Column(value = "succ_num")
    private BigDecimal succNum;

    /** 总核数<br/>Column: [total_num] */
    @Column(value = "total_num")
    private BigDecimal totalNum;

    /** 产品类型<br/>Column: [product_type] */
    @Column(value = "product_type")
    private String productType;

    /** uin<br/>Column: [uin] */
    @Column(value = "uin")
    private Long uin;
}
