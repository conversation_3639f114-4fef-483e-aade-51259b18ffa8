package cloud.demand.lab.modules.operation_view.entity.p2p;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * crp业务时间配置表
 */
@Data
@ToString
@Table("crp_event_notice_config")
public class CrpEventNoticeConfigDO extends BaseDO {


    /**
     * 事件编码<br/>Column: [event_code]
     */
    @Column(value = "event_code")
    private String eventCode;

    /**
     * 事件名称<br/>Column: [event_name]
     */
    @Column(value = "event_name")
    private String eventName;

    /**
     * 通知用户<br/>Column: [notice_user]
     */
    @Column(value = "notice_user")
    private String noticeUser;

    /**
     * 通知角色<br/>Column: [notice_role]
     */
    @Column(value = "notice_role")
    private String noticeRole;

    /**
     * 通知方式<br/>Column: [notice_way]
     */
    @Column(value = "notice_way")
    private String noticeWay;


    /**
     * 通知标题<br/>Column: [notice_title]
     */
    @Column(value = "notice_title")
    private String noticeTitle;

    /**
     * 通知内容<br/>Column: [notice_content]
     */
    @Column(value = "notice_content")
    private String noticeContent;

    /**
     *  测试环境时，可以收到通知的用户与此字段中的用户再取一次交集，防止测试环境将消息发送给业务人员引起麻烦，<br/>
     *  所以一般都是配置该业务的开发人员，对生产环境无效, <br/>
     *  为空时，表示不做过滤，不用与通知用户取交集 <br/>
     */
    @Column(value = "test_notice_user")
    private String testNoticeUser;

    /**
     *  是否关闭此通知， 默认值 false （0，不关闭）；true （1，表示关闭通知）
     */
    @Column(value = "turn_off")
    private Boolean turnOff;
}

