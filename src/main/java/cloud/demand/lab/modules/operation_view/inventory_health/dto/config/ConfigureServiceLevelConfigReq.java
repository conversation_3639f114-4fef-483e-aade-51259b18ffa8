package cloud.demand.lab.modules.operation_view.inventory_health.dto.config;

import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ConfigureServiceLevelConfigReq {
    @NotNull
    private String date;

    private List<Item> items;

    @Data
    public static class Item {
        @NotNull
        private Long id;
        @NotNull
        private BigDecimal num;
    }

}
