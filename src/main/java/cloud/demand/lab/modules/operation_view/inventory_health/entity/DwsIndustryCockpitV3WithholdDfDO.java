package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("dws_industry_cockpit_v3_withhold_df")
public class DwsIndustryCockpitV3WithholdDfDO {

    /** 分区键，代表数据版本<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 客户范围：1-内部,0-外部<br/>Column: [is_inner] */
    @Column(value = "is_inner")
    private Integer isInner;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 战区<br/>Column: [war_zone] */
    @Column(value = "war_zone")
    private String warZone;

    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    @Column(value = "crp_war_zone")
    private String crpWarZone;

    /** 客户简称<br/>Column: [un_customer_short_name] */
    @Column(value = "un_customer_short_name")
    private String unCustomerShortName;

    /** 实例型号<br/>Column: [instance_model] */
    @Column(value = "instance_model")
    private String instanceModel;

    /** appId<br/>Column: [app_id] */
    @Column(value = "app_id")
    private Long appId;

    /** uin<br/>Column: [uin] */
    @Column(value = "uin")
    private Long uin;

    /** approle<br/>Column: [approle] */
    @Column(value = "app_role")
    private String appRole;

    /** 预扣类型：弹性预扣/普通预扣<br/>Column: [reserve_mode] */
    @Column(value = "reserve_mode")
    private String reserveMode;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 规格型号<br/>Column: [specifications] */
    @Column(value = "specifications")
    private String specifications;

    /** GPU卡型<br/>Column: [gpu_card_type] */
    @Column(value = "gpu_card_type")
    private String gpuCardType;

    /** 创建日期<br/>Column: [create_time] */
    @Column(value = "create_time")
    private LocalDate createTime;

    /** 预扣时长<br/>Column: [withhold_duration] */
    @Column(value = "withhold_duration")
    private Integer withholdDuration;

    /** 预扣时长<br/>Column: [withhold_days] */
    @Column(value = "withhold_days")
    private Integer withholdDays;

    /** cpu核数，单位：核<br/>Column: [cpu_count] */
    @Column(value = "cpu_count")
    private Integer cpuCount;

    /** 内存大小,单位：G<br/>Column: [mem_count] */
    @Column(value = "mem_count")
    private Integer memCount;

    /** gpu卡数,卡<br/>Column: [gpu_count] */
    @Column(value = "gpu_count")
    private Integer gpuCount;

    /** CPU/GPU<br/>Column: [cpu_or_gpu] */
    @Column(value = "cpu_or_gpu")
    private String cpuOrGpu;

    /** cvm/baremetal<br/>Column: [biz_type] */
    @Column(value = "biz_type")
    private String bizType;

}
