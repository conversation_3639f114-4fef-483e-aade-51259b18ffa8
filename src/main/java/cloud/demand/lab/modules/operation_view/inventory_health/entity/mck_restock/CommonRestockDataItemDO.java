package cloud.demand.lab.modules.operation_view.inventory_health.entity.mck_restock;

import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.service.impl.DictServiceImpl;
import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;

@Data
public class CommonRestockDataItemDO {
    @Column("product_type")
    private String productType;
    @Column("instance_type_rename")
    private String instanceType;
    @Column("zone_name_rename")
    private String zoneName;
    @Column("year")
    private Integer year;
    @Column("week")
    private Integer week;
    @Column("date")
    private LocalDate date;
    @Column("cores")
    private BigDecimal cores;

    public static CommonRestockDataItemDO from(StatTimeItemDO statTimeItemDO) {
        CommonRestockDataItemDO itemDO = new CommonRestockDataItemDO();
        itemDO.setProductType(statTimeItemDO.getProductType());
        itemDO.setInstanceType(statTimeItemDO.getInstanceType());
        itemDO.setZoneName(statTimeItemDO.getZoneName());
        itemDO.setCores(statTimeItemDO.getCores());
        ResPlanHolidayWeekDO weekDO = SpringUtil.getBean(DictServiceImpl.class).getHolidayWeekInfoByDate(statTimeItemDO.getStatTime());
        if (weekDO != null) {
            itemDO.setYear(weekDO.getYear());
            itemDO.setWeek(weekDO.getWeek());
        } else {
            itemDO.setYear(0);
            itemDO.setWeek(0);
        }
        return itemDO;
    }

    @Data
    public static class StatTimeItemDO {
        @Column("product_type")
        private String productType;
        @Column("instance_type_rename")
        private String instanceType;
        @Column("zone_name_rename")
        private String zoneName;
        @Column("stat_time")
        private String statTime;
        @Column("cores")
        private BigDecimal cores;
    }
}
