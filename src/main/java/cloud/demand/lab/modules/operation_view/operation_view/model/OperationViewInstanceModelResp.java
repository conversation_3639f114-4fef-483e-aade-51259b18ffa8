package cloud.demand.lab.modules.operation_view.operation_view.model;

import cloud.demand.lab.common.excel.core.annotation.DotExcelEntity;
import cloud.demand.lab.common.excel.core.annotation.DotExcelField;
import cloud.demand.lab.modules.operation_view.inventory_health.constants.InventoryHealthExcelGroup;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OperationViewInstanceModelResp {

    /**数据*/
    private List<Item> data;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @DotExcelEntity
    public static class Item {
        /** 产品类型 */
        @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG, excelColumnName = "产品类型")
        private String productType;

        /** 境内外 */
        @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG, excelColumnName = "境内外")
        private String customhouseTitle;
        /** 区域名 */
        @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG, excelColumnName = "区域")
        private String areaName;
        /** 地域名 */
        @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG, excelColumnName = "地域")
        private String regionName;
        /** 可用区名 */
        @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG, excelColumnName = "可用区")
        private String zoneName;

        /**实例类型*/
        @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG, excelColumnName = "实例类型")
        private String instanceType;

        /**实例规格*/
        @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG, excelColumnName = "实例规格")
        private String instanceModel;

        /** 安全库存 */
        @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG, excelColumnName = "安全库存")
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal safeInv = null;

        /** 人工调整 */
        @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG, excelColumnName = "生效规格占比")
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal manualConfig = null;

        /** 净增计费规模 */
        @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG, excelColumnName = "净增计费规模")
        @JsonSerialize(using = BigDecimal4ScaleSerializer.class)
        private BigDecimal billScaleChange = BigDecimal.ZERO;

        /** 净增计费规模占比 */
        @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG, excelColumnName = "净增计费规模占比")
        @JsonSerialize(using = BigDecimal4ScaleSerializer.class)
        private BigDecimal billScaleChangeRatio = BigDecimal.ZERO;

        /** 净增服务规模 */
        @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG, excelColumnName = "净增服务规模")
        @JsonSerialize(using = BigDecimal4ScaleSerializer.class)
        private BigDecimal serveScaleChange = BigDecimal.ZERO;

        /** 净增服务规模占比 */
        @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG, excelColumnName = "净增服务规模占比")
        @JsonSerialize(using = BigDecimal4ScaleSerializer.class)
        private BigDecimal serveScaleChangeRatio = BigDecimal.ZERO;

        /** 期初计费规模 */
        @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG, excelColumnName = "期初计费规模")
        @JsonSerialize(using = BigDecimal4ScaleSerializer.class)
        private BigDecimal startBillScale = BigDecimal.ZERO;

        /** 期初服务规模 */
        @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG, excelColumnName = "期初服务规模")
        @JsonSerialize(using = BigDecimal4ScaleSerializer.class)
        private BigDecimal startServeScale = BigDecimal.ZERO;

        /** 期末计费规模 */
        @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG, excelColumnName = "期末计费规模")
        @JsonSerialize(using = BigDecimal4ScaleSerializer.class)
        private BigDecimal endBillScale = BigDecimal.ZERO;

        /** 期末服务规模 */
        @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG, excelColumnName = "期末服务规模")
        @JsonSerialize(using = BigDecimal4ScaleSerializer.class)
        private BigDecimal endServeScale = BigDecimal.ZERO;
    }
}
