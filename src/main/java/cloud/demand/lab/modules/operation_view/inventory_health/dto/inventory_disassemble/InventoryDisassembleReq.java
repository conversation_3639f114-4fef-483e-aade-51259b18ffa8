package cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble;

import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import lombok.Data;

@Data
public class InventoryDisassembleReq extends InventoryBasicReq{

    private String start;

    private String end;

    private List<String> dimension;

    private List<String> turnoverType;


    public WhereSQL genBasicCondition() {
        WhereSQL condition = new WhereSQL();
        if (ListUtils.isNotEmpty(zoneName)) {
            condition.and("zone_name in (?)", zoneName);
        }
        if (ListUtils.isNotEmpty(instanceType)) {
            condition.and("instance_type in (?)", instanceType);
        }
        if (ListUtils.isNotEmpty(areaName)) {
            condition.and("area_name in (?)", areaName);
        }
        if (ListUtils.isNotEmpty(regionName)) {
            condition.and("region_name in (?)", regionName);
        }
        if (ListUtils.isNotEmpty(customhouseTitle)) {
            condition.and("customhouse_title in (?)", customhouseTitle);
        }
        return condition;
    }
}
