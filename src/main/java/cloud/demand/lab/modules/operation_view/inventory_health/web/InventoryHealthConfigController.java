package cloud.demand.lab.modules.operation_view.inventory_health.web;

import cloud.demand.lab.modules.operation_view.entity.p2p.FileNameAndBytesDTO;
import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.ConfigureBufferPoolConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.ConfigureInstanceTypeConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.ConfigureServiceLevelConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.ConfigureZoneConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryBufferPoolConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryInstanceTypeConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryServiceLevelConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryZoneConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthConfigService;
import com.pugwoo.wooutils.lang.DateUtils;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

/**
 * 安全库存的参数配置，看需求 https://zhiyan.woa.com/requirement/6756/story/#/story?qs=%7B%22pagesize%22%3A20%2C%22search%22%3A%7B%22creator%22%3A%5B%5D%7D%2C%22pagecurrent%22%3A4%7D&task_tab=info&wsn=1632&wtype=story&wprojectid=6756&drawer_tab=info
 */
@Slf4j
@JsonrpcController("/inventory-health-config")
public class InventoryHealthConfigController {

    @Resource
    private InventoryHealthConfigService inventoryHealthConfigService;


    /**
     * 配置目标服务水平
     * @return
     */
    @RequestMapping
    public Object configureTargetServiceLevel(@JsonrpcParam ConfigureServiceLevelConfigReq req) {
        return inventoryHealthConfigService.configureTargetServiceLevel(req);
    }

    /**
     * 通过 EXCEL 导入服务水平
     */
    @RequestMapping
    public Object importTargetServiceLevel(@RequestParam("file") MultipartFile file, @RequestParam("date") String date) {

        Object res = inventoryHealthConfigService.importTargetServiceLevel(file, date);
        return res;
    }

    /**
     * 配置可用区
     * @return
     */
    @RequestMapping
    public Object configureZone(@JsonrpcParam ConfigureZoneConfigReq req) {
        return inventoryHealthConfigService.configureZone(req);
    }

    /**
     * 通过 EXCEL 导入可用区
     */
    @RequestMapping
    public Object importZoneConfig(@RequestParam("file") MultipartFile file, @RequestParam("date") String date) {

        Object res = inventoryHealthConfigService.importZone(file, date);
        return res;
    }

    /**
     * 配置机型
     */
    @RequestMapping
    public Object configureInstanceType(@JsonrpcParam ConfigureInstanceTypeConfigReq req) {
        return inventoryHealthConfigService.configureInstanceType(req);
    }

    /**
     * 通过 EXCEL 导入机型
     */
    @RequestMapping
    public Object importInstanceType(@RequestParam("file") MultipartFile file, @RequestParam("date") String date) {

        Object res = inventoryHealthConfigService.importInstanceType(file, date);
        return res;
    }

    /**
     * 查询目标可用区配置
     */
    @RequestMapping
    public Object queryZoneConfig(@JsonrpcParam QueryZoneConfigReq req) {
        return inventoryHealthConfigService.queryZoneConfig(req);
    }

    /**
     * 通过 Excel 导出目标可用区
     */
    @RequestMapping
    public Object exportZoneConfig(@JsonrpcParam Map<String, String> params) {
        String date = params.get("date");

        if (date == null) {
            throw new RuntimeException("date is null");
        }

        FileNameAndBytesDTO res = inventoryHealthConfigService.exportZoneConfig(date);
        return new DownloadBean(res.getFileName(), res.getBytes());
    }

    /**
     * 查询目标机型配置
     */
    @RequestMapping
    public Object queryInstanceTypeConfig(@JsonrpcParam QueryInstanceTypeConfigReq req) {
        return inventoryHealthConfigService.queryInstanceTypeConfig(req);
    }

    /**
     * 通过 Excel 导出目标机型
     */
    @RequestMapping
    public Object exportInstanceTypeConfig(@JsonrpcParam Map<String, String> params) {
        String date = params.get("date");

        if (date == null) {
            throw new RuntimeException("date is null");
        }

        FileNameAndBytesDTO res = inventoryHealthConfigService.exportInstanceTypeConfig(date);
        return new DownloadBean(res.getFileName(), res.getBytes());
    }

    /**
     * 查询目标服务水平
     */
    @RequestMapping
    public Object queryTargetServiceLevel(@JsonrpcParam QueryServiceLevelConfigReq req) {
        return inventoryHealthConfigService.queryTargetServiceLevel(req);
    }

    /**
     * 通过 Excel 导出目标服务水平
     */
    @RequestMapping
    public Object exportTargetServiceLevel(@JsonrpcParam Map<String, String> params) {
        String date = params.get("date");

        if (date == null) {
            throw new RuntimeException("date is null");
        }

        FileNameAndBytesDTO res = inventoryHealthConfigService.exportTargetServiceLevel(date);
        return new DownloadBean(res.getFileName(), res.getBytes());
    }

    /**
     * 手动触发 snapshot
     */
    @RequestMapping
    public Object snapshotInventoryHealthConfig(@JsonrpcParam Map<String, String> params) {
        String today = params.get("today");

        if (today == null) {
            today = DateUtils.formatDate(DateUtils.today());
        }

        log.info(today);

        inventoryHealthConfigService.snapshotInventoryHealthConfig(today);
        return null;
    }

    /**
     * 配置
     */
    @RequestMapping
    public Object configureBufferPool(@JsonrpcParam ConfigureBufferPoolConfigReq req) {
        return inventoryHealthConfigService.configureBufferPool(req);
    }

    /**
     * 通过 EXCEL 导入
     */
    @RequestMapping
    public Object importBufferPool(@RequestParam("file") MultipartFile file, @RequestParam("date") String date) {
        Object res = inventoryHealthConfigService.importBufferPool(file, date);
        return res;
    }

    /**
     * 查询配置
     */
    @RequestMapping
    public Object queryBufferPoolConfig(@JsonrpcParam QueryBufferPoolConfigReq req) {
        return inventoryHealthConfigService.queryBufferPoolConfig(req);
    }

    /**
     * 通过 Excel 导出目
     */
    @RequestMapping
    public Object exportBufferPoolConfig(@JsonrpcParam Map<String, String> params) {
        String date = params.get("date");

        if (date == null) {
            throw new RuntimeException("date is null");
        }

        FileNameAndBytesDTO res = inventoryHealthConfigService.exportBufferPoolConfig(date);
        return new DownloadBean(res.getFileName(), res.getBytes());
    }
}
