package cloud.demand.lab.modules.operation_view.operation_view.service;

import cloud.demand.lab.modules.operation_view.inventory_health.dto.BufferAverageCoreDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthZlkhbSafetyInventorySnapshotDO;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewExternalReq2;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewExternalResp2;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewInstanceModelReq;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewInstanceModelResp;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewReq2;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewResp2;
import java.util.Date;
import java.util.List;

public interface OperationViewService2 {

    /**
     * 运营视图2.0, 核心为安全库存的三种算法补全
     */
    OperationViewResp2 queryAllProductSummary(OperationViewReq2 req);

    /**
     * 新版运营视图主查询接口-for Outer Invoke
     * @param req
     */
    OperationViewExternalResp2 queryAllProductSummaryExternal(OperationViewExternalReq2 req);

    /**
     * 获取战略客户部的安全库存数据
     */
    List<InventoryHealthZlkhbSafetyInventorySnapshotDO> queryHeadZlkhbData(String statTime);

    /**
     * 获取腾讯云上报的近30日弹性规模数据
     */
    List<BufferAverageCoreDTO> queryBufferScaleCoreAverage(Date date);

    /**
     * 查询实例规格纬度的安全库存
     * @param req
     * @return
     */
    OperationViewInstanceModelResp queryAllProductSummaryInstanceModel(OperationViewInstanceModelReq req);
}
