package cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class QueryManualConfigDetailResp {
    private List<Item> data;
    @Data
    public static class Item{
        /**
         * 实例类型
         */
        private String instanceType;
        /**
         * 可用区名
         */
        private String zoneName;

        /**
         * 当前年
         */
        private Integer year;

        /**
         * 当前周
         */
        private Integer week;
        /**
         * 人工调整数值
         */
        private BigDecimal value;
        /**
         * 执行人工调整操作的原因
         */
        private String reason;

    }

}
