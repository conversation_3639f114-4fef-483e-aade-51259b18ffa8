package cloud.demand.lab.modules.operation_view.operation_view.service.impl;


import cloud.demand.lab.modules.operation_view.operation_view.enums.ClsLogProductEnum;
import cloud.demand.lab.modules.operation_view.operation_view.enums.ClsLogReqEnum;
import cloud.demand.lab.modules.operation_view.operation_view.model.ClsLogInfo;
import cloud.demand.lab.modules.operation_view.operation_view.service.ClsLogService;
import com.pugwoo.wooutils.collect.ListUtils;
import cs.easily.tp.cls.ClsLogReq;
import cs.easily.tp.cls.ClsLogUtils;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
public class ClsLogServiceImpl implements ClsLogService {

    @Override
    public List<ClsLogInfo> queryClsLog(ClsLogReq req) {
        List<ClsLogInfo> ret = ClsLogUtils.queryClsLogT(req, ClsLogInfo.class, true,true);
//        List<ClsLogInfo> ret = new ArrayList<>();
//        if (ListUtils.isNotEmpty(maps)){
//            for (Map<String, String> map : maps) {
//                ClsLogInfo info = new ClsLogInfo();
//                info.setAction(map.get("Action"));
//                info.setRegion(map.get("region"));
//                String total = map.get("total");
//                String insufficientResourceFailed = map.get("InsufficientResourceFailed");
//                String internalFailed = map.get("InternalFailed");
//                info.setTotal(Integer.valueOf(total));
//                info.setInsufficientResourceFailed(Integer.valueOf(insufficientResourceFailed));
//                info.setInternalFailed(Integer.valueOf(internalFailed));
//                ret.add(info);
//            }
//        }
        return ret;
    }

    @Override
    public List<ClsLogInfo> queryClsLog(ClsLogProductEnum product, LocalDate statTime) {
        List<ClsLogReq> reqs = ClsLogReqEnum.buildReq(product, statTime);
        List<ClsLogInfo> ret = new ArrayList<>();
        for (ClsLogReq req : reqs) {
            ret.addAll(queryClsLog(req));
        }
        return ret;
    }
}
