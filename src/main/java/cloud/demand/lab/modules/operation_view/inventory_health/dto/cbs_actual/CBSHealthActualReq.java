package cloud.demand.lab.modules.operation_view.inventory_health.dto.cbs_actual;


import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class CBSHealthActualReq {

    private String dateType;

    private String start;

    private String end;

    private List<String> customhouseTitle;

    private List<String> regionName;

    private List<String> country;

    private List<String> areaName;

    private List<String> zoneName;

    private List<String> zoneCategory;

    private String volumeType;

    private List<String> industryDept;

    private List<String> customerGroup;

    public WhereSQL genBasicCondition() {
        WhereSQL condition = new WhereSQL();
        if (ListUtils.isNotEmpty(regionName)) {
            condition.and("region_name in (?)", regionName);
        }
        if (ListUtils.isNotEmpty(zoneName)) {
            condition.and("zone_name in (?)", zoneName);
        }
        if (ListUtils.isNotEmpty(areaName)) {
            condition.and("area_name in (?)", areaName);
        }
        if (ListUtils.isNotEmpty(customhouseTitle)) {
            condition.and("customhouse_title in (?)", customhouseTitle);
        }
        if (StringUtils.isNotBlank(volumeType)) {
            condition.and("volume_type = ?", volumeType);
        }

        if (ListUtils.isNotEmpty(country)) {
            condition.and("country in (?)", country);
        }

        if (ListUtils.isNotEmpty(industryDept)) {
            condition.and("industry_dept in (?)", industryDept);
        }

        if (ListUtils.isNotEmpty(customerGroup)) {
            condition.and("customer_group in (?)", customerGroup);
        }

        condition.and("stat_time between ? and ?", start, end);
        return condition;
    }

}
