package cloud.demand.lab.modules.operation_view.entity.p2p;

import cloud.demand.lab.common.entity.BaseUserDO;
import cloud.demand.lab.modules.order.enums.Ppl13weekProductTypeEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.List;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("industry_demand_industry_war_zone_dict")
public class IndustryDemandIndustryWarZoneDictDO extends BaseUserDO {

    /**
     * 行业<br/>Column: [industry]
     */
    @Column(value = "industry")
    private String industry;

    /**
     * 战区名<br/>Column: [war_zone_name]
     */
    @Column(value = "war_zone_name")
    private String warZoneName;

    /**
     * 战区id
     */
    @Column(value = "war_zone_id")
    private Integer warZoneId;

    /**
     * 通用客户简称<br/>
     * Column: [common_customer_name]
     */
    @Column(value = "common_customer_name")
    private String commonCustomerName;

    /**
     * 客户名称<br/>Column: [customer_name]
     */
    @Column(value = "customer_name")
    private String customerName;

    /**
     * 是否为大客户<br/>
     * Column: [is_big_customer]
     */
    @Column(value = "is_big_customer")
    private Boolean isBigCustomer;

    /**
     * 是否启用<br/>
     * Column: [is_enable]
     */
    @Column(value = "is_enable")
    private Boolean isEnable;


    /**
     * 数据来源： input;import<br/>
     * Column: [data_source]
     */
    @Column(value = "data_source")
    private String dataSource;

    /**
     * 头部客户适用产品： CVM;GPU;EKS<br/>
     *
     * @see Ppl13weekProductTypeEnum .getCode()
     *         Column: [big_customer_product]
     */
    @Column(value = "big_customer_product")
    private String bigCustomerProduct;

    private List<String> bigCustomerProductList;

    /**
     * 是否为Top客户<br/>
     * Column: [is_top_customer]
     */
    @Column(value = "is_top_customer")
    private Boolean isTopCustomer;

}
