package cloud.demand.lab.modules.operation_view.inventory_health.dto;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Table("dws_cloud_server_level")
public class ZoneInsSlaDto {

    /**
     * 分区键，每天一版本<br/>Column: [version]
     */
    @Column(value = "version")
    private String version;

    @Column(value = "sumApi", computed = "sum(api_total)")
    private BigDecimal apiTotal;

    @Column(value = "sumApiSucc", computed = "sum(api_succ_total)")
    private BigDecimal apiSuccTotal;

    @Column(value = "sumSold", computed = "sum(sold_total)")
    private BigDecimal sumSold;

    @Column(value = "sumSoldOut", computed = "sum(sold_out_total)")
    private BigDecimal sumSoldOut;
}