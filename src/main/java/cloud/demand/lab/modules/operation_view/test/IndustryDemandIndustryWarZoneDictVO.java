package cloud.demand.lab.modules.operation_view.test;

import cloud.demand.lab.common.entity.BaseUserDO;
import cloud.demand.lab.modules.order.enums.Ppl13weekProductTypeEnum;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
public class IndustryDemandIndustryWarZoneDictVO {

    /**
     * 行业<br/>Column: [industry]
     */
    @Column(value = "industry")
    @ExcelProperty(value = "行业", index = 0)
    private String industry;

    /**
     * 战区名<br/>Column: [war_zone_name]
     */
    @Column(value = "war_zone_name")
    @ExcelProperty(value = "战区名", index = 1)
    private String warZoneName;

    /**
     * 战区id
     */
    @Column(value = "war_zone_id")
    @ExcelIgnore
    private Integer warZoneId;

    /**
     * 通用客户简称<br/>
     * Column: [common_customer_name]
     */
    @Column(value = "common_customer_name")
    @ExcelProperty(value = "通用客户简称", index = 2)
    private String commonCustomerName;

    /**
     * 客户名称<br/>Column: [customer_name]
     */
    @Column(value = "customer_name")
    @ExcelProperty(value = "客户简称", index = 3)
    private String customerName;

    /**
     * 是否为大客户<br/>
     * Column: [is_big_customer]
     */
    @Column(value = "is_big_customer")
    @ExcelProperty(value = "是否为大客户", index = 4)
    private Boolean isBigCustomer;

    /**
     * 是否启用<br/>
     * Column: [is_enable]
     */
    @Column(value = "is_enable")
    @ExcelProperty(value = "是否启用", index = 5)
    private Boolean isEnable;


    /**
     * 数据来源： input;import<br/>
     * Column: [data_source]
     */
    @Column(value = "data_source")
    @ExcelProperty(value = "数据来源", index = 6)
    private String dataSource;

    /**
     * 头部客户适用产品： CVM;GPU;EKS<br/>
     *
     * @see Ppl13weekProductTypeEnum .getCode()
     *         Column: [big_customer_product]
     */
    @Column(value = "big_customer_product")
    @ExcelProperty(value = "头部客户适用产品", index = 7)
    private String bigCustomerProduct;

    /**
     * 是否为Top客户<br/>
     * Column: [is_top_customer]
     */
    @Column(value = "is_top_customer")
    @ExcelProperty(value = "是否为Top客户", index = 8)
    private Boolean isTopCustomer;

    @ExcelProperty(value = "集团名称", index = 9)
    private String groupName;

    @ExcelProperty(value = "客户名称", index = 10)
    private String customerName1;

    @ExcelProperty(value = "业务OA部门", index = 11)
    private String businessManagerOaDept;

}
