package cloud.demand.lab.modules.operation_view.entity.cloud_crs;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("redis_devclass_config")
public class RedisDevclassConfigDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 腾讯标准机型代号，如B70，大写<br/>Column: [dev_class] */
    @Column(value = "dev_class")
    private String devClass;

    /** 0 物理机，1 CVM，2 容器<br/>Column: [dev_type] */
    @Column(value = "dev_type")
    private Integer devType;

    /** 物理总内存,单位GB<br/>Column: [mem_size] */
    @Column(value = "mem_size")
    private Integer memSize;

    /** 最大可用内存,单位GB<br/>Column: [mem_for_used] */
    @Column(value = "mem_for_used")
    private Integer memForUsed;

    /** 实际可分配内存,单位GB<br/>Column: [mem_for_redis] */
    @Column(value = "mem_for_redis")
    private Integer memForRedis;

    /** 扣除比例，物理机默认为20%，CVM默认为25%<br/>Column: [deduction_ratio] */
    @Column(value = "deduction_ratio")
    private BigDecimal deductionRatio;

    /** 是否是Intel AEP或BPS特殊设备,0 否，1 是<br/>Column: [is_aep] */
    @Column(value = "is_aep")
    private Integer isAep;

    /** 设备核心数<br/>Column: [cores] */
    @Column(value = "cores")
    private Integer cores;

    /** 核心数描述<br/>Column: [cores_desc] */
    @Column(value = "cores_desc")
    private String coresDesc;

    /** 设备带宽<br/>Column: [nic_speed_gb] */
    @Column(value = "nic_speed_gb")
    private Integer nicSpeedGb;

    /** 设备带宽描述<br/>Column: [nic_speed_desc] */
    @Column(value = "nic_speed_desc")
    private String nicSpeedDesc;

    /** 数据盘大小<br/>Column: [hard_disk_gb] */
    @Column(value = "hard_disk_gb")
    private Integer hardDiskGb;

    /** 设备磁盘容量描述<br/>Column: [hard_disk_desc] */
    @Column(value = "hard_disk_desc")
    private String hardDiskDesc;

    /** 设备价格<br/>Column: [svr_price] */
    @Column(value = "svr_price")
    private Integer svrPrice;

    /** 折损价格<br/>Column: [svr_discount_price] */
    @Column(value = "svr_discount_price")
    private Integer svrDiscountPrice;

}
