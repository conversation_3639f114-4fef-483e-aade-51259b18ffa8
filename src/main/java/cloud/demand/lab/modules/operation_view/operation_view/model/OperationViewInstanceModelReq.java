package cloud.demand.lab.modules.operation_view.operation_view.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class OperationViewInstanceModelReq {

    /** 选择日期 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;

    /** 规模开始日期 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date scaleStartDate;

    /** 规模结束日期 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date scaleEndDate;

    /** 客户类型，传code进来
     * @see cloud.demand.lab.modules.operation_view.inventory_health.enums.CustomerCustomGroupEnum
     */
    private String customerCustomGroup;

    /**实例类型*/
    private List<String> instanceType;

    /** 境内外 */
    private List<String> customhouseTitle;

    /** 区域 */
    private List<String> areaName;

    /** region */
    private List<String> regionName;

    /** 可用区 */
    private List<String> zoneName;

    /** 园区类型: 主力园区、在售非主力园区、其他 */
    private List<String> zoneCategory;

    /** 机型类型: 主力机型、在售非主力机型、其他 */
    private List<String> instanceTypeCategory;

    /** 仅看净增计费规模大于0 */
    private boolean onlyPositiveBillScale;

    /** 仅看净增服务规模大于0 */
    private boolean onlyPositiveServeScale;

    /** 人工调整占比最小值 */
    private BigDecimal minManualConfigValue;

    /** 人工调整占比最大值 */
    private BigDecimal maxManualConfigValue;

    public WhereSQL genCondition(){
        WhereSQL condition = new WhereSQL();
        if (ListUtils.isNotEmpty(instanceType)){
            condition.and("instance_type in (?)", instanceType);
        }
        if (ListUtils.isNotEmpty(customhouseTitle)){
            condition.and("customhouse_title in (?)", customhouseTitle);
        }
        if (ListUtils.isNotEmpty(areaName)){
            condition.and("area_name in (?)", areaName);
        }
        if (ListUtils.isNotEmpty(regionName)){
            condition.and("region_name in (?)", regionName);
        }
        if (ListUtils.isNotEmpty(zoneName)){
            condition.and("zone_name in (?)", zoneName);
        }
        return condition;
    }

}
