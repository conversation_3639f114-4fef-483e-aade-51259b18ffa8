package cloud.demand.lab.modules.operation_view.inventory_health.web;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cbs_actual.CBSHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.service.CBSHealthActualService;
import com.pugwoo.dbhelper.DBHelper;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@Slf4j
@JsonrpcController("/cbs-inventory-health")
public class CBSHealthActualController {

    @Resource
    CBSHealthActualService cbsHealthActualService;

    @Resource
    DBHelper ckcldDBHelper;

    @RequestMapping
    public Object queryCBSHealthActualTrendReport(@JsonrpcParam CBSHealthActualReq req) {
        if (req == null) {
            throw new WrongWebParameterException("请求参数为null");
        }
        return cbsHealthActualService.queryCBSHealthActualTrendReport(req);
    }

    @RequestMapping
    public DownloadBean exportCBSHealthActualTrendReport(@JsonrpcParam CBSHealthActualReq req) {
        if (req == null) {
            throw new WrongWebParameterException("请求参数为null");
        }
        return cbsHealthActualService.exportCBSHealthActualTrendReport(req);
    }

    @RequestMapping
    public Object getAllIndustryDept() {
        return ckcldDBHelper.getRaw(String.class, "select distinct industry_dept from dws_cbs_service_level_data_df");
    }
}
