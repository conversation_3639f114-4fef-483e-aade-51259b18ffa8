package cloud.demand.lab.modules.operation_view.entity.web.common;


import lombok.Data;

@Data
public class Result<T> {

    private int status;
    private String message;
    private T data;
    //  失败范围：部分失败-PART/全部失败-ALL
    private String errorType;

    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setStatus(0);
        result.setMessage("success");
        result.setData(data);
        return result;
    }

    public static <T> Result<T> fail(int status, String message) {
        Result<T> result = new Result<>();
        result.setStatus(status);
        result.setMessage(message);
        return result;
    }


    public static <T> Result<T> fail(int status, String message, String errorType) {
        Result<T> result = new Result<>();
        result.setStatus(status);
        result.setMessage(message);
        result.setErrorType(errorType);
        return result;
    }
}