package cloud.demand.lab.modules.operation_view.entity.yunti;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("cloud_demand_csig_resource_view_category")
public class CloudDemandCsigResourceViewCategoryDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**
     * 删除标记<br/>Column: [deleted]
     */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    /**
     * 创建时间<br/>Column: [create_time]
     */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /**
     * 更新时间<br/>Column: [update_time]
     */
    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;

    /**
     * 大类<br/>Column: [category_1]
     */
    @Column(value = "category_1")
    private String category1;

    /**
     * 中类<br/>Column: [category_2]
     */
    @Column(value = "category_2")
    private String category2;

    /**
     * 子类<br/>Column: [category_3]
     */
    @Column(value = "category_3")
    private String category3;

    /**
     * 规划产品<br/>Column: [plan_product_name]
     */
    @Column(value = "plan_product_name")
    private String planProductName;

    /**
     * CPU/GPU<br/>Column: [compute_type]
     */
    @Column(value = "compute_type")
    private String computeType;

    /**
     * 需求视图大类<br/>Column: [category_5]
     */
    @Column(value = "category_4")
    private String category4;

    /**
     * 需求视图产品分类 <br/>Column: [category_5]
     */
    @Column(value = "category_5")
    private String category5;

    @Column(value = "product")
    private String product;
}
