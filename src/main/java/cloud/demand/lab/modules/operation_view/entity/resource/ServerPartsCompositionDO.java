package cloud.demand.lab.modules.operation_view.entity.resource;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

/**
 * 服务器部件构成、部件缩写策略表
 */
@Data
@ToString
@Table("server_parts_composition")
public class ServerPartsCompositionDO {

    /** 主键<br/>Column: [id] */
    @Column(value = "id", isAutoIncrement = true)
    private Integer id;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 设备版本<br/>Column: [device_version] */
    @Column(value = "device_version")
    private String deviceVersion;

    /** 处理器型号缩写<br/>Column: [cpu_abbr] */
    @Column(value = "cpu_abbr")
    private String cpuAbbr;

    /** 处理器型号<br/>Column: [cpu_model] */
    @Column(value = "cpu_model")
    private String cpuModel;

    /** 单处理器核心数量<br/>Column: [cpu_core] */
    @Column(value = "cpu_core")
    private Integer cpuCore;

    /** 处理器数量<br/>Column: [cpu_number] */
    @Column(value = "cpu_number")
    private Integer cpuNumber;

    /** 内存缩写<br/>Column: [memory_abbr] */
    @Column(value = "memory_abbr")
    private String memoryAbbr;

    /** 内存属性<br/>Column: [memory_memo] */
    @Column(value = "memory_memo")
    private String memoryMemo;

    /** 单内存容量(GB)<br/>Column: [memory_volume] */
    @Column(value = "memory_volume")
    private Integer memoryVolume;

    /** 内存条数<br/>Column: [memory_number] */
    @Column(value = "memory_number")
    private Integer memoryNumber;

    /** 硬盘缩写<br/>Column: [disk_abbr] */
    @Column(value = "disk_abbr")
    private String diskAbbr;

    /** 硬盘接口类型<br/>Column: [disk_type] */
    @Column(value = "disk_type")
    private String diskType;

    /** 硬盘规格(GB)<br/>Column: [disk_volume] */
    @Column(value = "disk_volume")
    private Integer diskVolume;

    /** 硬盘数量<br/>Column: [disk_number] */
    @Column(value = "disk_number")
    private Integer diskNumber;

    /** 硬盘尺寸<br/>Column: [disk_size] */
    @Column(value = "disk_size")
    private String diskSize;

    /** 硬盘2缩写<br/>Column: [disk2_abbr] */
    @Column(value = "disk2_abbr")
    private String disk2Abbr;

    /** 硬盘2接口类型<br/>Column: [disk2_type] */
    @Column(value = "disk2_type")
    private String disk2Type;

    /** 硬盘2规格(GB)<br/>Column: [disk2_volume] */
    @Column(value = "disk2_volume")
    private Integer disk2Volume;

    /** 硬盘2数量<br/>Column: [disk2_number] */
    @Column(value = "disk2_number")
    private Integer disk2Number;

    /** 硬盘2尺寸<br/>Column: [disk2_size] */
    @Column(value = "disk2_size")
    private String disk2Size;

    /** 网卡缩写<br/>Column: [nic_abbr] */
    @Column(value = "nic_abbr")
    private String nicAbbr;

    /** 网卡速率<br/>Column: [nic_speed] */
    @Column(value = "nic_speed")
    private String nicSpeed;

    /** 网卡接口类型<br/>Column: [nic_type] */
    @Column(value = "nic_type")
    private String nicType;

    /** 网卡数量<br/>Column: [nic_number] */
    @Column(value = "nic_number")
    private Integer nicNumber;

    /** 网卡2缩写<br/>Column: [nic2_abbr] */
    @Column(value = "nic2_abbr")
    private String nic2Abbr;

    /** 网卡2速率<br/>Column: [nic2_speed] */
    @Column(value = "nic2_speed")
    private String nic2Speed;

    /** 网卡2接口类型<br/>Column: [nic2_type] */
    @Column(value = "nic2_type")
    private String nic2Type;

    /** 网卡2数量<br/>Column: [nic2_number] */
    @Column(value = "nic2_number")
    private Integer nic2Number;

    /** SSD缩写<br/>Column: [ssd_abbr] */
    @Column(value = "ssd_abbr")
    private String ssdAbbr;

    /** SSD属性<br/>Column: [ssd_memo] */
    @Column(value = "ssd_memo")
    private String ssdMemo;

    /** SSD规格<br/>Column: [ssd_type] */
    @Column(value = "ssd_type")
    private Integer ssdType;

    /** SSD接口类型<br/>Column: [ssd_port_type] */
    @Column(value = "ssd_port_type")
    private String ssdPortType;

    /** SSD数量<br/>Column: [ssd_number] */
    @Column(value = "ssd_number")
    private Integer ssdNumber;

    /** SSD2缩写<br/>Column: [ssd2_abbr] */
    @Column(value = "ssd2_abbr")
    private String ssd2Abbr;

    /** SSD3缩写<br/>Column: [ssd3_abbr] */
    @Column(value = "ssd3_abbr")
    private String ssd3Abbr;

    /** SSD2属性<br/>Column: [ssd2_memo] */
    @Column(value = "ssd2_memo")
    private String ssd2Memo;

    /** SSD2规格<br/>Column: [ssd2_type] */
    @Column(value = "ssd2_type")
    private Integer ssd2Type;

    /** SSD2接口类型<br/>Column: [ssd2_port_type] */
    @Column(value = "ssd2_port_type")
    private String ssd2PortType;

    /** SSD2数量<br/>Column: [ssd2_number] */
    @Column(value = "ssd2_number")
    private Integer ssd2Number;

    /** GPU缩写<br/>Column: [gpu_abbr] */
    @Column(value = "gpu_abbr")
    private String gpuAbbr;

    /** GPU型号<br/>Column: [gpu_model] */
    @Column(value = "gpu_model")
    private String gpuModel;

    /** GPU数量<br/>Column: [gpu_number] */
    @Column(value = "gpu_number")
    private Integer gpuNumber;

    /** 是否默认版本{1:是,0:否}<br/>Column: [default_flag] */
    @Column(value = "default_flag")
    private Integer defaultFlag;

    /** 更新时间<br/>Column: [update_time] */
    @Column(value = "update_time")
    private Date updateTime;

}
