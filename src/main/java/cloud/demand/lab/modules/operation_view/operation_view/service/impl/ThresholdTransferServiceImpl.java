package cloud.demand.lab.modules.operation_view.operation_view.service.impl;

import cloud.demand.lab.modules.operation_view.entity.web.common.Result;
import cloud.demand.lab.common.task_log.service.TaskLogService;
import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.operation_view.entity.InventoryHealthThresholdConfigDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.InventoryHealthThresholdConfigLogDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.InventoryHealthThresholdConfigSnapshotDO;
import cloud.demand.lab.modules.operation_view.operation_view.model.SafetyInvThresholdReq;
import cloud.demand.lab.modules.operation_view.operation_view.model.TransferThresholdDTO;
import cloud.demand.lab.modules.operation_view.operation_view.service.ThresholdTransferService;
import cloud.demand.lab.modules.operation_view.util.tencent_cloud.YunXiaoUtil;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ThresholdTransferServiceImpl implements ThresholdTransferService {

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DictService baseDictService;
    @Resource
    private TaskLogService taskLogService;

    @Override
    public Result setSafetyInvThreshold(SafetyInvThresholdReq req, String submitor) {
        List<SafetyInvThresholdReq.Item> data = req.getData();

        /**
         * 这里和前端约定：
         * errorMsgs有多条时，按 \n 分隔行
         * 其他情况下按照 xxxx,xxxx来提示报错信息
         * 逗号前为失败项如：{上海五1区: S5}，逗号后为失败原因如：没有[上海五1区]相关的信息
         *
         */
        List<String> errorMsgs = Lang.list();

        //  获取腾讯云全量ZoneInfo
        List<StaticZoneDO> allZoneInfos = baseDictService.getAllZoneInfos();

        //  构造云霄阈值设置接口需要的入参对象
        List<TransferThresholdDTO> dtoList = batchBuild(data, allZoneInfos, errorMsgs);
        if (ListUtils.isEmpty(dtoList)){
            return Result.fail(-1, "安全库存阈值设置失败-可用区查询失败", "ALL");
        }

        List<InventoryHealthThresholdConfigDO> result = transferThresholdToYunxiao(dtoList, errorMsgs);
        if (ListUtils.isEmpty(result)){
            //  确定了云霄接口的规则：如果设置成功，success一定为true，否则认为设置失败
            return Result.fail(-2, StringTools.join("\n", errorMsgs), "ALL");
        }
        //  覆盖阈值配置 & 记录操作log
        overwriteAndLog(result, submitor);
        if (ListUtils.isNotEmpty(errorMsgs)){
            return Result.fail(-3, StringTools.join("\n", errorMsgs), "PART");
        }
        return Result.success("安全库存阈值设置成功");
    }

    @Override
    public void snapshotThresholdConfig() {
        Date yesterday = DateUtils.addTime(new Date(), Calendar.DATE, -1);
        //  保证幂等性
        long count = demandDBHelper.getCount(InventoryHealthThresholdConfigSnapshotDO.class,
                "where stat_time = ?", DateUtils.formatDate(yesterday));
        if (count != 0){
            demandDBHelper.delete(InventoryHealthThresholdConfigSnapshotDO.class,
                    "where stat_time = ?", DateUtils.formatDate(yesterday));
        }
        List<InventoryHealthThresholdConfigDO> all = demandDBHelper.getAll(InventoryHealthThresholdConfigDO.class);
        List<InventoryHealthThresholdConfigSnapshotDO> result = Lang.list();
        ListUtils.forEach(all, o -> {
            InventoryHealthThresholdConfigSnapshotDO one = InventoryHealthThresholdConfigSnapshotDO.transform(o);
            one.setStatTime(DateUtils.toLocalDate(yesterday));
            result.add(one);
        });
        demandDBHelper.insertBatchWithoutReturnId(result);
    }

    @Override
    public Map<String, BigDecimal> queryRealTimeThreshold() {
        List<InventoryHealthThresholdConfigDO> thresholdList = demandDBHelper.getAll(InventoryHealthThresholdConfigDO.class);
        return ListUtils.toMap(thresholdList, o ->
                        Strings.join("@", o.getZoneName(), o.getInstanceType()),
                InventoryHealthThresholdConfigDO::getThreshold);
    }

    @Override
    @HiSpeedCache(expireSecond = 6000, continueFetchSecond = 36000, keyScript = "args[0]")
    public Map<String, BigDecimal> querySnapshotThreshold(String statTime) {
        List<InventoryHealthThresholdConfigSnapshotDO> thresholdList =
                demandDBHelper.getAll(InventoryHealthThresholdConfigSnapshotDO.class, "where stat_time = ?", statTime);
        return ListUtils.toMap(thresholdList, o ->
                        Strings.join("@", o.getZoneName(), o.getInstanceType()),
                InventoryHealthThresholdConfigSnapshotDO::getThreshold);
    }

    /**
     * 生成云霄需要传递的DTO数据
     */
    private List<TransferThresholdDTO> batchBuild(List<SafetyInvThresholdReq.Item> data,
            List<StaticZoneDO> allZoneInfos, List<String> errorMsgs){
        Map<String, StaticZoneDO> map = ListUtils.toMap(allZoneInfos, StaticZoneDO::getZoneName, o -> o);
        List<TransferThresholdDTO> transform = ListUtils.transform(data, item -> {
            String zoneName = item.getZoneName();
            String instanceType = item.getInstanceType();
            //  获取zoneName对应的zone信息
            StaticZoneDO staticZone = map.get(zoneName);
            if (staticZone == null) {
                String msg = String.format("{%s:%s}, 可用区信息[%s]查询失败", zoneName, instanceType, zoneName);
                taskLogService.genRunLog("setSafetyInvThreshold", "batchBuild", msg);
                errorMsgs.add(msg);
                return null;
            }
            return new TransferThresholdDTO().
                    setRegion(staticZone.getApiRegion()).
                    setZone(staticZone.getZone()).
                    setInstanceFamily(item.getInstanceType()).
                    setCustomhouseTitle(staticZone.getCustomhouseTitle()).setAreaName(staticZone.getAreaName()).
                    setRegionName(staticZone.getRegionName()).setZoneName(staticZone.getZoneName()).
                    setThresholdValue(item.getThresholdValue());
        });
        return ListUtils.filter(transform, Objects::nonNull);
    }

    /**
     * 将安全库存的阈值传递给云产品的接口
     * 2023-07-26 由于设置后云官网实施生效，保险起见这里单个单个设置
     */
    public List<InventoryHealthThresholdConfigDO> transferThresholdToYunxiao(
            List<TransferThresholdDTO> dtoList, List<String> errorMsgs){

        //  云霄-云产品安全库存阈值设置接口
        String url = "/beacon/cvm-type-config/safe-limit-quota/batch-set";
        List<InventoryHealthThresholdConfigDO> result = Lang.list();

        //  单个调用云霄的接口
        for (TransferThresholdDTO dto : dtoList) {
            if (dto == null) {
                continue;
            }

            Map<String, Object> param = MapUtils.of(
                    "region", dto.getRegion(),
                    "zone", dto.getZone(),
                    "instanceFamily", dto.getInstanceFamily(),
                    "quota", dto.getThresholdValue());

            String respJson = YunXiaoUtil.postRaw(url, param);

            //  解析云霄接口的响应体
            Map<String, Object> map = JSON.parseToMap(respJson);
            if (map == null) {
                errorMsgs.add(String.format("{%s:%s},云官网安全库存设置失败-云霄设置接口无响应数据",
                        dto.getZone(), dto.getInstanceFamily()));
                continue;
            }
            if (((Boolean) map.get("success"))){
                InventoryHealthThresholdConfigDO configDO = new InventoryHealthThresholdConfigDO();
                configDO.setCustomhouseTitle(dto.getCustomhouseTitle());
                configDO.setAreaName(dto.getAreaName());
                configDO.setRegionName(dto.getRegionName());
                configDO.setZoneName(dto.getZoneName());
                configDO.setRegion(dto.getRegion());
                configDO.setZone(dto.getZone());
                configDO.setInstanceType(dto.getInstanceFamily());
                configDO.setThreshold(NumberUtils.parseBigDecimal(dto.getThresholdValue()));
                result.add(configDO);
            } else {
                String errorMsg = JSON.toJson(respJson);
                errorMsgs.add(String.format("{%s:%s},云官网阈值设置失败信息:%s",
                        dto.getZone(), dto.getInstanceFamily(), map.get("message")));
                taskLogService.genRunLog("setSafetyInvThreshold", "setSafetyInvThreshold",
                        "云霄阈值设置接口调用失败,报错明细为:" + errorMsg);
            }
        }
        return result;
    }


    /**
     * 覆盖安全库存阈值实时表并将操作记录到日志表中
     */
    public void overwriteAndLog(List<InventoryHealthThresholdConfigDO> result, String submitor){
        if (ListUtils.isEmpty(result)){
            return;
        }
        //  用来存储操作记录
        List<InventoryHealthThresholdConfigLogDO> configLogList = Lang.list();
        //  用来存储修改后的操作数据
        for (InventoryHealthThresholdConfigDO target : result) {
            //  找到原本生效的那一条配置，同一机型-可用区维度下配置唯一
            String instanceType = target.getInstanceType();
            String zoneName = target.getZoneName();
            WhereSQL condition = new WhereSQL();
            condition.and("zone_name = ?", zoneName);
            condition.and("instance_type = ?", instanceType);
            InventoryHealthThresholdConfigDO origin = demandDBHelper.
                    getOne(InventoryHealthThresholdConfigDO.class, condition.getSQL(), condition.getParams());
            //  记录操作类型
            String operationType;
            if (origin == null){
                operationType = "NEW";
            } else {
                if (BigDecimal.ZERO.compareTo(target.getThreshold()) == 0) {
                    operationType = "DELETE";
                } else {
                    operationType = "UPDATE";
                }
            }
            InventoryHealthThresholdConfigLogDO logDO = new InventoryHealthThresholdConfigLogDO();
            logDO.setOperationType(operationType);
            logDO.setSubmitor(submitor);
            logDO.setTargetStatusJson(JSON.toJson(target));
            if (!Objects.equals("NEW", operationType)) {    //  新增没有origin数据
                logDO.setOriginRowId(origin.getId());
                logDO.setOriginStatusJson(JSON.toJson(origin));
                if (decimalEquals(origin.getThreshold(), target.getThreshold())){
                    //  如果相同维度下的新旧阈值相同,记录为未改变的
                    //  此时就只是记录动作，数据并没有发生任何变化
                    logDO.setOperationType("UNCHANGED");
                    configLogList.add(logDO);
                    continue;
                }
            }
            demandDBHelper.delete(InventoryHealthThresholdConfigDO.class, condition.getSQL(), condition.getParams());
            demandDBHelper.insert(target);
            //  拿到自增主键回设targetRowId
            logDO.setTargetRowId(target.getId());
            configLogList.add(logDO);
        }
        demandDBHelper.insert(configLogList);
    }

    /**
     * 比较两个BigDecimal类型的数据是否数值相等
     */
    public static boolean decimalEquals(BigDecimal num1, BigDecimal num2){
        if (num1 == null){
            num1 = BigDecimal.ZERO;
        }
        if (num2 == null){
            num2 = BigDecimal.ZERO;
        }
        return num1.compareTo(num2) == 0;
    }




}
