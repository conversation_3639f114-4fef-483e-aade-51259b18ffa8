package cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual;

import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.BigDataTypeEnum;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.OperationViewService2Impl;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import java.util.List;
import lombok.Data;

@Data
public class BigDataHealthTrendReq {

    private String dateType;

    private String start;

    private String end;

    private String product;

    private List<String> zoneName;

    private List<String> instanceType;

    private List<String> zoneCategory;

    private List<String> instanceCategory;

    private List<String> areaName;

    private List<String> regionName;

    private List<String> customhouseTitle;

    private List<String> ginsFamily;

    private List<String> country;

    public WhereSQL genBasicCondition() {
        WhereSQL condition = new WhereSQL();
        if (ListUtils.isNotEmpty(zoneName)) {
            condition.and("zone_name in (?)", zoneName);
        }
        if (ListUtils.isNotEmpty(instanceType)) {
            condition.and("instance_family in (?)", instanceType);
        }
        if (StringTools.isNotBlank(product)) {
            condition.and("product_type = ?", BigDataTypeEnum.getNameByCode(product));
        }
        if (ListUtils.isNotEmpty(areaName)) {
            condition.and("area_name in (?)", areaName);
        }
        if (ListUtils.isNotEmpty(regionName)) {
            condition.and("region_name in (?)", regionName);
        }
        if (ListUtils.isNotEmpty(customhouseTitle)) {
            condition.and("customhouse_title in (?)", customhouseTitle);
        }
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        WhereSQL categoryCondition = bean.genBigDataCategoryCondition(zoneCategory, instanceCategory, false,
                DateUtils.formatDate(DateUtils.yesterday()), customhouseTitle);
        condition.and(categoryCondition);
        condition.and("stat_time  between ? and ?", start, end);
        return condition;
    }

}
