package cloud.demand.lab.modules.operation_view.operation_view.entity;

import cloud.demand.lab.common.entity.BaseDO;
import lombok.Data;

@Data
public class DemandWeekNConfigDO extends BaseDO {
    public DemandWeekNConfigDO(
            String instanceType,
            String customhouseTitle,
            int weekN
    ) {
        this.instanceType = instanceType;
        this.customhouseTitle = customhouseTitle;
        this.weekN = weekN;
    }
    /**
     * 实例类型
     */
    private String instanceType;
    /**
     * 境内外
     */
    private String customhouseTitle;

    /**
     * week N 配置，如果 weekN == 5，表示 5 周前（按节假周计算）
     */
    private Integer weekN;
}
