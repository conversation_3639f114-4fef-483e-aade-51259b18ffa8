package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("cbs_service_level_processed_raw_data")
public class CbsServiceLevelProcessedRawDataDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    @Column(value = "operation_date")
    private LocalDate operationDate;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "region")
    private String region;

    @Column(value = "cbs_zone_name")
    private String cbsZoneName;

    @Column(value = "cbs_zone_id")
    private Integer cbsZoneId;

    @Column(value = "appid")
    private Integer appid;

    @Column(value = "organization_name")
    private String organizationName;

    @Column(value = "customer_group")
    private String customerGroup;

    @Column(value = "volume_type")
    private String volumeType;

    @Column(value = "pay_mode")
    private String payMode;

    @Column(value = "pool")
    private String pool;

    @Column(value = "success_count")
    private Integer successCount;

    @Column(value = "failed_count")
    private Integer failedCount;

    @Column(value = "success_disk_size")
    private Integer successDiskSize;

    @Column(value = "fail_disk_size")
    private Integer failDiskSize;

    @Column(value = "region_type")
    private String regionType;

    @Column(value = "add_time")
    private LocalDateTime addTime;

}
