package cloud.demand.lab.modules.operation_view.inventory_health.dto.config;

import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class QueryBufferPoolConfigReq {
    @NotNull
    private String date;

    /**
     * 境内外
     */
    private List<String> customhouseTitle;

    /**
     * 实例类型
     */
    private List<String> instanceType;


    public WhereSQL genCondition() {
        WhereSQL condition = new WhereSQL();
        condition.and("date = ?", date);
        // 默认只查询 CVM
        condition.and("(product is null or product = ?)", "CVM");

        if (ListUtils.isNotEmpty(customhouseTitle)) {
            condition.and("region_type in (?)", customhouseTitle);
        }

        if (ListUtils.isNotEmpty(instanceType)) {
            condition.and("instance_type in (?)", instanceType);
        }

        return condition;
    }
}
