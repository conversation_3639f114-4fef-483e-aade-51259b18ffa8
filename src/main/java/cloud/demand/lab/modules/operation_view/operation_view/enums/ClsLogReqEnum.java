package cloud.demand.lab.modules.operation_view.operation_view.enums;

import cloud.demand.lab.modules.operation_view.operation_view.config.DynamicProperties;
import cs.easily.tp.cls.ClsLogReq;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;
import lombok.AllArgsConstructor;
import lombok.Getter;
import yunti.boot.exception.ITException;

/** cls日志查询枚举
 * 文档：<a href="https://doc.weixin.qq.com/doc/w3_AQUA9QbnAI8G18EE7GSSH0tpQBUVX?scode=AJEAIQdfAAoWu0QeVgAekAsAaVAPU">...</a>
 * ap-nanjing：新加坡
 * ap-nanjing：南京
 * */
@Getter
@AllArgsConstructor
public enum ClsLogReqEnum {
    CLB_DOM(ClsLogProductEnum.CLB, "ap-nanjing","e93a5b9b-d532-485d-866b-0edd8ee69023"
            ,"toInterfaceName:\"CreateLoadBalancer\" | select __TAG__.region as region, toInterfaceName as Action, count_if(status!=0 and split(rsp, '''')[8]='InternalError') as InternalFailed, count_if(status!=0 and (split(rsp, '''')[8]='FailedOperation') and ((regexp_like(split(rsp, '''', 11)[11],'no available vip')=true and regexp_like(split(rsp, '''', 11)[11],'ap-guangzhou-5')=false and regexp_like(split(rsp, '''', 11)[11],'ap-shanghai-1')=false and regexp_like(split(rsp, '''', 11)[11],'ap-beijing-1')=false and regexp_like(split(rsp, '''', 11)[11],'ap-beijing-2')=false and regexp_like(split(rsp, '''', 11)[11],'ap-beijing-4')=false) or regexp_like(split(rsp, '''', 11)[11],'has not enough resource')=true)) as InsufficientResourceFailed, count(*) as total group by region, toInterfaceName limit 10000"),
    CLB_INTL(ClsLogProductEnum.CLB, "ap-singapore","9b4da91c-57db-4635-8f23-e0fb3af28b01"
            ,"toInterfaceName:\"CreateLoadBalancer\" | select __TAG__.region as region, toInterfaceName as Action, count_if(status!=0 and split(rsp, '''')[8]='InternalError') as InternalFailed, count_if(status!=0 and (split(rsp, '''')[8]='FailedOperation') and ((regexp_like(split(rsp, '''', 11)[11],'no available vip')=true and regexp_like(split(rsp, '''', 11)[11],'ap-guangzhou-5')=false and regexp_like(split(rsp, '''', 11)[11],'ap-shanghai-1')=false and regexp_like(split(rsp, '''', 11)[11],'ap-beijing-1')=false and regexp_like(split(rsp, '''', 11)[11],'ap-beijing-2')=false and regexp_like(split(rsp, '''', 11)[11],'ap-beijing-4')=false) or regexp_like(split(rsp, '''', 11)[11],'has not enough resource')=true)) as InsufficientResourceFailed, count(*) as total group by region, toInterfaceName limit 10000"),
    EIP_DOM_P1(ClsLogProductEnum.EIP, "ap-nanjing","f5cbb164-429d-4c74-b3e6-ec33861e26ee"
            ,"toInterfaceName:\"_LazyAssociateNormalAddress\" AND \"\\\"Operate\\\":\\\"APPLY\\\"\" | select  __TAG__.region as region, '_LazyAssociateNormalAddress' as Interface,count_if(code='InternalError') as internalError,count_if(code='ResourceInsufficient') as resourceInsufficient , count(*) as total group by __TAG__.region limit 10000"),
    EIP_DOM_P2(ClsLogProductEnum.EIP, "ap-nanjing","f5cbb164-429d-4c74-b3e6-ec33861e26ee"
            ,"toInterfaceName:\"AllocateNetworkForHAI\" OR toInterfaceName:\"AssociateNormalAddressForLighthouse\" OR toInterfaceName:\"AssociateNormalAddress\" OR toInterfaceName:\"AllocateAddresses\" OR toInterfaceName:\"_AssociateNormalAddress\" OR toInterfaceName:\"AdjustPublicAddress\" | select  __TAG__.region as region, toInterfaceName as Interface, count_if(code='InternalError') as internalError,count_if(code='ResourceInsufficient') as resourceInsufficient , count(*) as total group by __TAG__.region, Interface limit 10000"),
    EIP_INTL_P1(ClsLogProductEnum.EIP, "ap-singapore","42cfdc7c-6cc3-4a57-bdda-d0ff60e49620"
            ,"toInterfaceName:\"_LazyAssociateNormalAddress\" AND \"\\\"Operate\\\":\\\"APPLY\\\"\" | select  __TAG__.region as region, '_LazyAssociateNormalAddress' as Interface,count_if(code='InternalError') as internalError,count_if(code='ResourceInsufficient') as resourceInsufficient , count(*) as total group by __TAG__.region limit 10000"),
    EIP_INTL_P2(ClsLogProductEnum.EIP, "ap-singapore","42cfdc7c-6cc3-4a57-bdda-d0ff60e49620"
            ,"toInterfaceName:\"AllocateNetworkForHAI\" OR toInterfaceName:\"AssociateNormalAddressForLighthouse\" OR toInterfaceName:\"AssociateNormalAddress\" OR toInterfaceName:\"AllocateAddresses\" OR toInterfaceName:\"_AssociateNormalAddress\" OR toInterfaceName:\"AdjustPublicAddress\" | select  __TAG__.region as region, toInterfaceName as Interface, count_if(code='InternalError') as internalError,count_if(code='ResourceInsufficient') as resourceInsufficient , count(*) as total group by __TAG__.region, Interface limit 10000"),

    ;
    private final ClsLogProductEnum productEnum;
    private final String region;
    private final String topicId;
    private final String query;


    /**
     * 构建请求
     * @param productEnum 产品枚举
     * @return
     */
    public static List<ClsLogReq> buildReq(ClsLogProductEnum productEnum, LocalDate statTime){
        if (productEnum == null){
            throw new ITException("产品枚举不能为空");
        }
        if (statTime == null){
            statTime = LocalDate.now().plusDays(-1); // 默认昨天
        }
        List<ClsLogReq> ret = new ArrayList<>();
        String clsLogSecretId = DynamicProperties.clsLogSecretId.get();
        String clsLogSecretKey = DynamicProperties.clsLogSecretKey.get();
        for (ClsLogReqEnum value : values()) {
            if (value.getProductEnum() == productEnum){
                ClsLogReq clsLogReq = new ClsLogReq();
                clsLogReq.setSecretId(clsLogSecretId);
                clsLogReq.setSecretKey(clsLogSecretKey);
                clsLogReq.setStartTime(LocalDateTime.of(statTime, LocalTime.MIN));
                clsLogReq.setEndTime(LocalDateTime.of(statTime, LocalTime.MAX));
                clsLogReq.setTopicId(value.getTopicId());
                clsLogReq.setQuery(value.getQuery());
                clsLogReq.setRegion(value.getRegion());
                ret.add(clsLogReq);
            }
        }
        return ret;
    }
}
