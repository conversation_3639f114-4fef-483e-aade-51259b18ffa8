package cloud.demand.lab.modules.operation_view.inventory_health.web;

import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.ConfigureMckRestockManualConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryManualConfigDetailReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryManualConfigDetailResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryMckRestockDetailReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryMckRestockForecastReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryMckRestockSupplyDetailResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryMckRestockForecastDetailResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryMckRestockInventoryDetailResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryMckRestockReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryMckRestockResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryOperationActionDetailResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryTotalDetailSummaryReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryTotalDetailSummaryResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.safety_inventory.OperationViewSoeReq;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthMckRestockService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.TestInventoryService;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.CvmType;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.OutsideViewOldService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@JsonrpcController("/inventory-health")
public class InventoryHealthMckRestockController {
    @Resource
    InventoryHealthMckRestockService mckRestockService;

    @Resource
    TestInventoryService testInventoryService;

    @Resource
    OutsideViewOldService outsideViewOldService;

    @RequestMapping
    public QueryMckRestockResp queryMckRestockReport(@JsonrpcParam QueryMckRestockReq req) {
        return mckRestockService.queryMckRestockReport(req);
    }

    @RequestMapping
    public QueryMckRestockInventoryDetailResp queryMckRestockReportInventoryDetail(@JsonrpcParam QueryMckRestockDetailReq req) {
        return mckRestockService.queryMckRestockReportInventoryDetail(req);
    }

    @RequestMapping
    public QueryMckRestockSupplyDetailResp queryMckRestockReportSupplyDetail(@JsonrpcParam QueryMckRestockDetailReq req) {
        List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
        return mckRestockService.queryMckRestockReportSupplyDetail(req, allCvmType);
    }

    @RequestMapping
    public QueryMckRestockForecastDetailResp queryMckRestockReportForecastDetail(@JsonrpcParam QueryMckRestockForecastReq req) {
        List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
        return mckRestockService.queryMckRestockReportForecastDetail(req, allCvmType);
    }

    /**
     * 缺口与运营动作相关联
     */
    @RequestMapping
    public QueryOperationActionDetailResp queryOperationActionDetail(@JsonrpcParam QueryMckRestockDetailReq req) {
        List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
        return mckRestockService.queryOperationActionDetail(req, allCvmType);
    }

    /**
     * 人工调整
     */
    @RequestMapping
    public Object mckRestockManualConfig(@JsonrpcParam ConfigureMckRestockManualConfigReq req) {
        return mckRestockService.configureMckRestockManualConfig(req);
    }


    @RequestMapping
    public QueryManualConfigDetailResp queryManualConfigDetail(@JsonrpcParam QueryManualConfigDetailReq req){
        return mckRestockService.queryManualConfigDetail(req);
    }

    @RequestMapping
    public String getActualAndSafeInv(@JsonrpcParam OperationViewSoeReq req) {
        testInventoryService.getActualAndSafeInv(req, "historyWeekPeakForecastWN");
        return "ok";
    }
    @RequestMapping
    public QueryTotalDetailSummaryResp queryTotalDetailSummary(@JsonrpcParam QueryTotalDetailSummaryReq req) {
        return mckRestockService.queryTotalDetailSummary(req);
    }




}
