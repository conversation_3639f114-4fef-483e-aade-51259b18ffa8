package cloud.demand.lab.modules.operation_view.operation_view.service.impl;

import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.operation_view.entity.web.common.Result;
import cloud.demand.lab.common.task_log.service.TaskLogService;
import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.operation_view.entity.InventoryHealthManualConfigDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.InventoryHealthManualConfigLogDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.InventoryHealthManualConfigSnapshotDO;
import cloud.demand.lab.modules.operation_view.operation_view.model.SafetyInvManualReq;
import cloud.demand.lab.modules.operation_view.operation_view.service.ManualConfigService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ManualConfigServiceImpl implements ManualConfigService {

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DictService baseDictService;
    @Resource
    private TaskLogService taskLogService;

    @Override
    public Result setSafetyInvManual(SafetyInvManualReq req, String submitor) {
        List<SafetyInvManualReq.Item> data = req.getData();

        /**
         * 这里和前端约定：
         * errorMsgs有多条时，按 \n 分隔行
         * 其他情况下按照 xxxx,xxxx来提示报错信息
         * 逗号前为失败项如：{上海五1区: S5}，逗号后为失败原因如：没有[上海五1区]相关的信息
         *
         */
        List<String> errorMsgs = Lang.list();

        //  获取腾讯云全量ZoneInfo
        List<StaticZoneDO> allZoneInfos = baseDictService.getAllZoneInfos();

        //  构造入参对象
        List<InventoryHealthManualConfigDO> result = batchBuild(data, allZoneInfos, errorMsgs);
        if (ListUtils.isEmpty(result)){
            return Result.fail(-1, "安全库存人工调整设置失败-可用区查询失败", "ALL");
        }

        //  人工调整更新后，从当天开始覆盖到这周的周日
        Date now = DateUtils.parse(DateUtils.yesterday().toString());
        ResPlanHolidayWeekDO holidayWeekDO = baseDictService.getHolidayWeekInfoByDate(req.getDate());
        Date end = DateUtils.parse(holidayWeekDO.getEnd());
        Date date = DateUtils.parse(req.getDate());
        while(!date.after(now) && !date.after(end)) {
            //  覆盖配置 & 记录操作log
            overwriteAndLog(DateUtils.formatDate(date), result, submitor);
            date = DateUtils.addTime(date, Calendar.DATE, 1);
        }

        if (ListUtils.isNotEmpty(errorMsgs)){
            return Result.fail(-3, StringTools.join("\n", errorMsgs), "PART");
        }
        return Result.success("安全库存人工调整设置成功");
    }

    @Override
    public void snapshotManualConfig(String date) {
        List<InventoryHealthManualConfigSnapshotDO> current = demandDBHelper.getAll(InventoryHealthManualConfigSnapshotDO.class,
                "where stat_time = ?", date);

        // 确保幂等性
        if (current.size() > 0){
            demandDBHelper.delete(InventoryHealthManualConfigSnapshotDO.class, "where stat_time = ?", date);
        }

        Date yesterday = DateUtils.addTime(DateUtils.parse(date), Calendar.DATE, -1);
        List<InventoryHealthManualConfigSnapshotDO> all = demandDBHelper.getAll(InventoryHealthManualConfigSnapshotDO.class,
                "where stat_time = ?", DateUtils.formatDate(yesterday));
        // 把前一天的继承下来
        if (all.size() > 0) {
            List<InventoryHealthManualConfigSnapshotDO> result = Lang.list();
            ListUtils.forEach(all, o -> {
                InventoryHealthManualConfigSnapshotDO one = InventoryHealthManualConfigSnapshotDO.transform(o);
                one.setStatTime(DateUtils.parseLocalDate(date));
                result.add(one);
            });
            demandDBHelper.insertBatchWithoutReturnId(result);
        }
//        if (count != 0){
//            demandDBHelper.delete(InventoryHealthManualConfigSnapshotDO.class,
//                    "where stat_time = ?", DateUtils.formatDate(yesterday));
//        }
//        List<InventoryHealthManualConfigDO> all = demandDBHelper.getAll(InventoryHealthManualConfigDO.class);
//        List<InventoryHealthManualConfigSnapshotDO> result = Lang.list();
//        ListUtils.forEach(all, o -> {
//            InventoryHealthManualConfigSnapshotDO one = InventoryHealthManualConfigSnapshotDO.transform(o);
//            one.setStatTime(DateUtils.toLocalDate(yesterday));
//            result.add(one);
//        });
//        demandDBHelper.insertBatchWithoutReturnId(result);
    }

//    @Override
//    public Map<String, BigDecimal> queryRealTimeManual() {
//        List<InventoryHealthManualConfigDO> manualList = demandDBHelper.getAll(InventoryHealthManualConfigDO.class);
//        return ListUtils.toMap(manualList, o ->
//                Strings.join("@", o.getZoneName(), o.getInstanceType()),
//                InventoryHealthManualConfigDO::getNum);
//    }

    @Override
    public Map<String, BigDecimal> querySnapshotManual(String statTime) {
        List<InventoryHealthManualConfigSnapshotDO> manualList =
                demandDBHelper.getAll(InventoryHealthManualConfigSnapshotDO.class, "where stat_time = ?", statTime);
        return ListUtils.toMap(manualList, o ->
                        Strings.join("@", o.getZoneName(), o.getInstanceType()),
                InventoryHealthManualConfigSnapshotDO::getNum);
    }

    /**
     * 生成云霄需要传递的DTO数据
     */
    private List<InventoryHealthManualConfigDO> batchBuild(List<SafetyInvManualReq.Item> data,
            List<StaticZoneDO> allZoneInfos, List<String> errorMsgs){
        Map<String, StaticZoneDO> map = ListUtils.toMap(allZoneInfos, StaticZoneDO::getZoneName, o -> o);
        List<InventoryHealthManualConfigDO> transform = ListUtils.transform(data, item -> {
            String zoneName = item.getZoneName();
            String instanceType = item.getInstanceType();
            //  获取zoneName对应的zone信息
            StaticZoneDO staticZone = map.get(zoneName);
            if (staticZone == null) {
                String msg = String.format("{%s:%s}, 可用区信息[%s]查询失败", zoneName, instanceType, zoneName);
                taskLogService.genRunLog("setSafetyInvManual", "batchBuild", msg);
                errorMsgs.add(msg);
                return null;
            }
            return new InventoryHealthManualConfigDO().
                    setCustomhouseTitle(staticZone.getCustomhouseTitle()).
                    setAreaName(staticZone.getAreaName()).
                    setRegionName(staticZone.getRegionName()).
                    setZoneName(staticZone.getZoneName()).
                    setInstanceType(item.getInstanceType()).
                    setNum(BigDecimal.valueOf(item.getValue()));
        });
        return ListUtils.filter(transform, Objects::nonNull);
    }

    /**
     * 覆盖安全库存人工调整实时表并将操作记录到日志表中
     */
    public void overwriteAndLog(String date, List<InventoryHealthManualConfigDO> result, String submitor){
        if (ListUtils.isEmpty(result)){
            return;
        }
        //  用来存储操作记录
        List<InventoryHealthManualConfigLogDO> configLogList = Lang.list();
        //  用来存储修改后的操作数据
        for (InventoryHealthManualConfigDO target : result) {
            //  找到原本生效的那一条配置，同一机型-可用区维度下配置唯一
            String instanceType = target.getInstanceType();
            String zoneName = target.getZoneName();
            WhereSQL condition = new WhereSQL();
            condition.and("zone_name = ?", zoneName);
            condition.and("instance_type = ?", instanceType);
            condition.and("stat_time = ?", date);
//            InventoryHealthManualConfigDO origin = demandDBHelper.
//                    getOne(InventoryHealthManualConfigDO.class, condition.getSQL(), condition.getParams());
            InventoryHealthManualConfigSnapshotDO snapshot = demandDBHelper.
                    getOne(InventoryHealthManualConfigSnapshotDO.class, condition.getSQL(), condition.getParams());
            String operationType;
            InventoryHealthManualConfigSnapshotDO origin = null;

            if (snapshot != null) {
                origin = InventoryHealthManualConfigSnapshotDO.transform(snapshot);
                snapshot.setNum(target.getNum());

                if (BigDecimal.ZERO.compareTo(target.getNum()) == 0) {
                    operationType = "DELETE";
                } else {
                    operationType = "UPDATE";
                }
            } else {
                InventoryHealthManualConfigSnapshotDO newSnapshot = InventoryHealthManualConfigSnapshotDO.transform(target);
                newSnapshot.setStatTime(DateUtils.toLocalDate(DateUtils.parse(date)));

                operationType = "NEW";
                snapshot = newSnapshot;
            }
            //  记录操作类型
//            String operationType;
//            if (origin == null){
//                operationType = "NEW";
//            } else {
//                if (BigDecimal.ZERO.compareTo(target.getNum()) == 0) {
//                    operationType = "DELETE";
//                } else {
//                    operationType = "UPDATE";
//                }
//            }
            InventoryHealthManualConfigLogDO logDO = new InventoryHealthManualConfigLogDO();
            logDO.setOperationType(operationType);
            logDO.setSubmitor(submitor);
            logDO.setTargetStatusJson(JSON.toJson(snapshot));
            if (!Objects.equals("NEW", operationType)) {    //  新增没有origin数据
                logDO.setOriginRowId(origin.getId());
                logDO.setOriginStatusJson(JSON.toJson(origin));
                if (decimalEquals(snapshot.getNum(), origin.getNum())){
                    //  如果相同维度下的新旧相同,记录为未改变的
                    //  此时就只是记录动作，数据并没有发生任何变化
                    logDO.setOperationType("UNCHANGED");
                    configLogList.add(logDO);
                    continue;
                }
            }
            if (snapshot.getId() != null && snapshot.getId() > 0) {
                demandDBHelper.update(snapshot);
            } else {
                demandDBHelper.insert(snapshot);
            }

            //  拿到自增主键回设targetRowId
            logDO.setTargetRowId(snapshot.getId());
            configLogList.add(logDO);
        }
        demandDBHelper.insert(configLogList);
    }

    /**
     * 比较两个BigDecimal类型的数据是否数值相等
     */
    public static boolean decimalEquals(BigDecimal num1, BigDecimal num2){
        if (num1 == null){
            num1 = BigDecimal.ZERO;
        }
        if (num2 == null){
            num2 = BigDecimal.ZERO;
        }
        return num1.compareTo(num2) == 0;
    }




}
