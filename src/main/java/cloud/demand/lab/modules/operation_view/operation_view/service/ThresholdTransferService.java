package cloud.demand.lab.modules.operation_view.operation_view.service;

import cloud.demand.lab.modules.operation_view.entity.web.common.Result;
import cloud.demand.lab.modules.operation_view.operation_view.model.SafetyInvThresholdReq;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 安全库存阈值相关的service接口
 */
public interface ThresholdTransferService {

    /**
     * 安全库存阈值设置接口
     * @return
     */
    Result setSafetyInvThreshold(SafetyInvThresholdReq req, String submitor);

    /**
     * 安全库存阈值配置切片
     */
    void snapshotThresholdConfig();

    /**
     * 查询安全库存实时值
     * @return Map: k: 可用区+机型 v:对应维度下的阈值
     */
    Map<String, BigDecimal> queryRealTimeThreshold();

    /**
     * 根据日期查询安全库存阈值切片
     * @return Map: k: 可用区+机型 v:对应维度下的阈值
     */
    Map<String, BigDecimal> querySnapshotThreshold(String statTime);


}
