package cloud.demand.lab.modules.operation_view.operation_view.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

/**
 * 库存健康-安全库存阈值下发云产品 实时表
 */
@Data
@ToString
@Table("inventory_health_threshold_config")
public class InventoryHealthThresholdConfigDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    @JsonIgnore
    private Long id;

    /** 删除标记<br/>Column: [deleted] */
    @Column(value = "deleted", softDelete = {"0", "1"})
    @JsonIgnore
    private Boolean deleted;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    @JsonIgnore
    private Date createTime;

    /** 任务结束时的时间<br/>Column: [update_time] */
    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    @JsonIgnore
    private Date updateTime;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 地区名<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 地域名<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区名<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** region(exp:ap-guangzhou)(for云产品接口传递)<br/>Column: [region] */
    @Column(value = "region")
    private String region;

    /** zone(exp:ap-guangzhou-4)(for云产品接口传递)<br/>Column: [zone] */
    @Column(value = "zone")
    private String zone;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 安全库存阈值<br/>Column: [threshold] */
    @Column(value = "threshold")
    private BigDecimal threshold;

}
