package cloud.demand.lab.modules.operation_view.operation_view.model;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import lombok.Data;

@Data
public class BufferScaleDTO {

    @Column(value = "stat_time")
    private LocalDate statTime;

    @Column(value = "instance_type")
    private String instanceType;

    /** 可用区名<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** region名<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    @Column(value = "bufferCores")
    private String bufferCores;

}
