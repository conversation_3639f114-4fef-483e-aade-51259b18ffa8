package cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class EKSDataHealthActualData {

    private String date;

    private String zoneName;

    private String areaName;

    private String regionName;

    private String customhouseTitle;

    private String product;

    private String zoneCategory;

    private BigDecimal apiSucTotal;

    private BigDecimal apiTotal;

    private BigDecimal apiSucRate;
}
