package cloud.demand.lab.modules.operation_view.inventory_health.dto;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;

@Data
@Table("report_plan_detail")
public class CkReportPlanDetailSumVO {

    @Column(value = "stat_time")
    private LocalDate statTime;

    @Column(value = "instance_type")
    private String instanceType;

    /** 可用区名<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** region名<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    @Column(value = "sumCores", computed = "sum(cores)")
    private BigDecimal sumCores;

    @Column(value = "sumNum", computed = "sum(num)")
    private BigDecimal sumNum;

    @Column(value = "sumLogicNum", computed = "sum(logic_num)")
    private BigDecimal sumLogicNum;

    @Column(value = "sumBufferCores", computed = "sum(buffer_cores)")
    private BigDecimal sumBufferCores;
}
