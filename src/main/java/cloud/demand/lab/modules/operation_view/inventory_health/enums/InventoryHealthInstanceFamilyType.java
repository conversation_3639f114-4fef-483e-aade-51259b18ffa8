package cloud.demand.lab.modules.operation_view.inventory_health.enums;

/**
 * 安全库存机型一级类型，跟云霄同步
 */
public enum InventoryHealthInstanceFamilyType {
    PRINCIPAL("PRINCIPAL", "主力机型"),
    EOL("EOL", "EOL机型"),
    OTHER("<PERSON><PERSON><PERSON>", "其他机型");

    String code;
    String name;

    InventoryHealthInstanceFamilyType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameFromCode(String code) {
        for (InventoryHealthInstanceFamilyType status : InventoryHealthInstanceFamilyType.values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return null;
    }

    public static String getCodeFromName(String name) {
        for (InventoryHealthInstanceFamilyType status : InventoryHealthInstanceFamilyType.values()) {
            if (status.getName().equals(name)) {
                return status.getCode();
            }
        }
        return null;
    }
}
