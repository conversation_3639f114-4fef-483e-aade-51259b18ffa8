package cloud.demand.lab.modules.operation_view.inventory_health.service;

import cloud.demand.lab.modules.operation_view.entity.p2p.FileNameAndBytesDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.TrendGraphReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.TrendGraphResp;

public interface TrendGraphService {

    /**
     * 库存健康页面数据趋势图服务
     *
     * @param req
     * @return
     */
    TrendGraphResp queryInventoryHealthTrendGraph(TrendGraphReq req);

    FileNameAndBytesDTO exportPurchaseDetailExcel(TrendGraphReq req);

    FileNameAndBytesDTO exportMoveDetailExcel(TrendGraphReq req);
}
