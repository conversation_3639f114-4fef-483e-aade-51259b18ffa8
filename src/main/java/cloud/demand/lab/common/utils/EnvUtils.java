package cloud.demand.lab.common.utils;

import com.pugwoo.wooutils.string.StringTools;

/**
 * 判断当前的环境
 */
public class EnvUtils {

    private static final String STAGE = System.getenv("STAGE");
    private static final String TASK_NODE = System.getenv("TASK_NODE");
    private static final String POD_NAME = System.getenv("POD_NAME");


    /**
     * 获取pod 的名称
     */
    public static String getPodName() {
        return POD_NAME;
    }

    /**
     * 本地环境：为空或DEV
     * 测试环境：TEST
     * 生产环境：PROD
     */
    public static String getStage() {
        return STAGE;
    }

    /**
     * 是否是本地开发环境
     */
    public static boolean isLocalEnv() {
        return StringTools.isBlank(STAGE) || "DEV".equalsIgnoreCase(STAGE);
    }

    /**
     * 判断是否是生产环境
     */
    public static boolean isProduction() {
        return "PROD".equalsIgnoreCase(STAGE) || "TKEx-TEG-PROD".equalsIgnoreCase(STAGE)
                || "PROD2".equalsIgnoreCase(STAGE);
    }

    /**
     * 查询是否是任务节点，默认是false，只有当明确给了定时任务TASK_NODE=true才返回true
     */
    public static boolean isTaskNode() {
        return "true".equalsIgnoreCase(TASK_NODE);
    }

}