package cloud.demand.lab.common.utils;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 */
@Slf4j
public class TimeUtils {

    /**
     * Log the execution time of the code inside the given Runnable using SLF4J, along with the class name and line
     * number
     * where the method was called.
     *
     * @param codeToTrack The code to be executed and have its execution time logged.
     * @param info The info text to be displayed along with the execution time.
     */
    public static <T> T printExecutionTime(String info, Supplier<T> codeToTrack) {
        StackTraceElement caller = Thread.currentThread().getStackTrace()[2];
        String callerInfo = String.format("%s:%d", caller.getClassName(), caller.getLineNumber());

        long startTime = System.nanoTime();
        T result = codeToTrack.get();
        long endTime = System.nanoTime();
        long durationInMillis = TimeUnit.MILLISECONDS.convert(endTime - startTime, TimeUnit.NANOSECONDS);
        log.info(" printExecutionTime - {}: {} ms, {} ", info, durationInMillis, callerInfo);

        return result;
    }

    /**
     * Log the execution time of the code inside the given Runnable using SLF4J, along with the class name and line
     * number
     * where the method was called.
     *
     * @param codeToTrack The code to be executed and have its execution time logged.
     * @param info The info text to be displayed along with the execution time.
     */
    public static void printExecutionTime(String info, Runnable codeToTrack) {
        StackTraceElement caller = Thread.currentThread().getStackTrace()[2];
        String callerInfo = String.format("%s:%d", caller.getClassName(), caller.getLineNumber());

        long startTime = System.nanoTime();
        codeToTrack.run();
        long endTime = System.nanoTime();
        long durationInMillis = TimeUnit.MILLISECONDS.convert(endTime - startTime, TimeUnit.NANOSECONDS);
        log.info(" printExecutionTime - {}: {} ms, {} ", info, durationInMillis, callerInfo);
    }
}
