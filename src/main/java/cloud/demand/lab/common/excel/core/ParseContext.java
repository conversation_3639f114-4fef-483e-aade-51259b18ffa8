package cloud.demand.lab.common.excel.core;

import cloud.demand.lab.common.excel.core.checker.ExcelColumnValueChecker;
import cloud.demand.lab.common.excel.core.checker.ExcelResultDataAfterConvertChecker;
import cloud.demand.lab.common.excel.core.checker.ExcelRowDataAfterConvertChecker;
import com.alibaba.excel.converters.Converter;
import java.util.List;
import java.util.Map;

public class ParseContext<T> {

    /** 业务自定义上下文信息 */
    private Map<String, Object> customContext;

    private Map<String, String> fieldNameAndColumnNameMap;

    private Map<String, Integer> columnNameAndIndexMap;

    private Map<String, List<ExcelColumnValueChecker>> customValueCheckers;

    private List<ExcelRowDataAfterConvertChecker<? super T>> customRowCheckers;

    private List<ExcelResultDataAfterConvertChecker<? super T>> customResultCheckers;

    private Map<String, Converter<?>> columnConvertMap;

    private int headRowNumber = -1;

    // 从 1 开始，小于1表示当前没有在解析具体的某一行
    int currentRow = -1;

    // 从 1 开始，小于1表示当前没有在解析具体的某一列
    int currentColumn = -1;

    // 当前正在解析的字段信息
    ExcelFieldInfo currentFieldInfo = null;

    /**
     *   设置当前处理哪一行哪一列、什么字段信息
     * @param row 从 1 开始，小于1或null表示当前没有在解析具体的某一行
     * @param column 从 1 开始，小于1或null表示当前没有在解析具体的某一列
     * @param fieldInfo 当前在解析的字段信息
     */
    void currentParseInfoSet(Integer row, Integer column, ExcelFieldInfo fieldInfo) {
        currentRow = row == null || row < 1 ? -1 : row;
        currentColumn = column == null || column < 1 ? -1 : column;
        currentFieldInfo = fieldInfo;
    }

    public int getHeadRowNumber() {
        return headRowNumber;
    }

    void setHeadRowNumber(int headRowNumber) {
        this.headRowNumber = headRowNumber;
    }

    public Map<String, Object> getCustomContext() {
        return customContext;
    }

    void setCustomContext(Map<String, Object> customContext) {
        this.customContext = customContext;
    }

    public Map<String, String> getFieldNameAndColumnNameMap() {
        return fieldNameAndColumnNameMap;
    }

    void setFieldNameAndColumnNameMap(Map<String, String> fieldNameAndColumnNameMap) {
        this.fieldNameAndColumnNameMap = fieldNameAndColumnNameMap;
    }

    public Map<String, Integer> getColumnNameAndIndexMap() {
        return columnNameAndIndexMap;
    }

    void setColumnNameAndIndexMap(Map<String, Integer> columnNameAndIndexMap) {
        this.columnNameAndIndexMap = columnNameAndIndexMap;
    }

    public Map<String, List<ExcelColumnValueChecker>> getCustomValueCheckers() {
        return customValueCheckers;
    }

    void setCustomValueCheckers(Map<String, List<ExcelColumnValueChecker>> customValueCheckers) {
        this.customValueCheckers = customValueCheckers;
    }

    public List<ExcelRowDataAfterConvertChecker<? super T>> getCustomRowCheckers() {
        return customRowCheckers;
    }

    void setCustomRowCheckers(
            List<ExcelRowDataAfterConvertChecker<? super T>> customRowCheckers) {
        this.customRowCheckers = customRowCheckers;
    }

    public List<ExcelResultDataAfterConvertChecker<? super T>> getCustomResultCheckers() {
        return customResultCheckers;
    }

    void setCustomResultCheckers(
            List<ExcelResultDataAfterConvertChecker<? super T>> customResultCheckers) {
        this.customResultCheckers = customResultCheckers;
    }

    public Map<String, Converter<?>> getColumnConvertMap() {
        return columnConvertMap;
    }

    void setColumnConvertMap(Map<String, Converter<?>> columnConvertMap) {
        this.columnConvertMap = columnConvertMap;
    }
}
