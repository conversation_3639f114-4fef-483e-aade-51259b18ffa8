package cloud.demand.lab.common.excel.core;

import com.alibaba.excel.exception.ExcelCommonException;
import java.io.Serializable;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.SneakyThrows;
import org.apache.commons.collections4.MapUtils;

/**
 * 数据校验结果
 */
public class ErrorMessage {

    /**
     * 行下标
     */
    private Integer row;

    /**
     * 列下标
     */
    private Integer col;

    /**
     * 列名
     */
    private String colName;

    /**
     * 提示消息
     */
    private String message;

    public ErrorMessage() {
    }

    public ErrorMessage(Integer row, Integer col, String colName, String message) {
        this.row = row;
        this.col = col;
        this.colName = colName;
        this.message = message;
    }

    @SneakyThrows
    public ErrorMessage(Integer row, ParseContext context, IGetter getter, String message) {
        this.row = row;
        this.col = getExcelColumnIndex(getter, context);
        this.colName = getExcelColumnName(getter, context);
        this.message = message;
    }


    /**
     * 只支持 String 的方法
     */
    public static ErrorMessage makeErrorIfBlank(int raw, IGetter fn, Object data, ParseContext context)
            throws ReflectiveOperationException {
        Object columnValue = getFieldValue(fn, data);
        return makeErrorIfBlank(raw, getExcelColumnIndex(fn, context), getExcelColumnName(fn, context), columnValue);
    }

    public static ErrorMessage makeErrorIfBlank(int raw, IGetter fn, Object data, ParseContext context,
            String msgPrefix)
            throws ReflectiveOperationException {
        Object columnValue = getFieldValue(fn, data);
        return makeErrorIfBlank(raw, getExcelColumnIndex(fn, context), getExcelColumnName(fn, context),
                columnValue, msgPrefix);
    }

    public static ErrorMessage makeErrorIfBlank(int raw, int columnIndex, String columnName, Object columnValue) {
        return makeErrorIfBlank(raw, columnIndex, columnName, columnValue, "");
    }

    public static ErrorMessage makeErrorIfBlank(int raw, int columnIndex, String columnName, Object columnValue,
            String msgPrefix) {
        if (columnValue == null) {
            return new ErrorMessage(raw, columnIndex, columnName, msgPrefix + "【" + columnName + "】值为空");
        }
        if (columnValue instanceof String) {
            columnValue = ((String) columnValue).trim();
            if (((String) columnValue).isEmpty()) {
                return new ErrorMessage(raw, columnIndex, columnName, msgPrefix + "【" + columnName + "】值为空");
            }
        } else {
            throw new ExcelCommonException("不支持类型");
        }
        return null;
    }

    public static ErrorMessage makeErrorIfNotNumber(int raw, int columnIndex, String columnName, Object columnValue,
            String msgPrefix) {
        ErrorMessage errorMessage = makeErrorIfBlank(raw, columnIndex, columnName, columnValue);
        if (errorMessage != null) {
            return errorMessage;
        }
        if (columnValue instanceof String) {
            columnValue = ((String) columnValue).trim();
            try {
                int i = Double.valueOf(columnValue.toString()).intValue();
            } catch (Exception e) {
                return new ErrorMessage(raw, columnIndex, columnName,
                        msgPrefix + "【" + columnName + "】非数字，请输入数字");
            }
        } else {
            throw new ExcelCommonException("不支持类型");
        }
        return null;
    }

    public static ErrorMessage makeErrorIfNotContain(int raw, IGetter fn, List<String> all, Object data,
            ParseContext context) throws ReflectiveOperationException {
        Object columnValue = getFieldValue(fn, data);
        String columnName = getExcelColumnName(fn, context);
        int columnIndex = getExcelColumnIndex(fn, context);
        return makeErrorIfNotContain(raw, columnIndex, columnName, columnValue, all);
    }

    public static ErrorMessage makeErrorIfNotContain(int raw, IGetter fn, List<String> all, Object data,
            ParseContext context, String msgPrefix) throws ReflectiveOperationException {
        Object columnValue = getFieldValue(fn, data);
        String columnName = getExcelColumnName(fn, context);
        int columnIndex = getExcelColumnIndex(fn, context);
        return makeErrorIfNotContain(raw, columnIndex, columnName, columnValue, all, msgPrefix);
    }

    /**
     * 1.判断数值是否为非空
     *
     * 2.判断是否在范围内
     */
    public static ErrorMessage makeErrorIfNumberNotInRange(int raw, int columnIndex, String columnName,
            Object columnValue, Integer start, Integer end) {
        ErrorMessage errorMessage = makeErrorIfNotNumber(raw, columnIndex, columnName, columnValue, "");
        if (errorMessage != null) {
            return errorMessage;
        }

        Integer number = Double.valueOf(columnValue.toString()).intValue();

        String errorMsg = "【" + columnName + " : " + number + "】范围需在 "
                + "[" + (start == null ? "-∞" : start) + "," + (end == null ? "+∞" : start) + "]";

        if (start != null) {
            if (number < start) {
                return new ErrorMessage(raw, columnIndex, columnName, errorMsg);
            }
        }

        if (end != null) {
            if (number > end) {
                return new ErrorMessage(raw, columnIndex, columnName, errorMsg);
            }
        }
        return null;
    }


    /**
     * 1.判断数值是否为非空
     *
     * 2.判断是否在范围内
     */
    public static ErrorMessage makeErrorIfNotRegexp(int raw, int columnIndex, String columnName,
            Object columnValue, String regexp) {
        ErrorMessage errorMessage = makeErrorIfBlank(raw, columnIndex, columnName, columnValue);
        if (errorMessage != null) {
            return errorMessage;
        }
        String str = ((String) columnValue).trim();
        Pattern pattern = Pattern.compile(regexp);
        Matcher matcher = pattern.matcher(str);
        boolean found = false;
        while (matcher.find()) {
            found = true;
        }
        if (!found) {
            return new ErrorMessage(raw, columnIndex, columnName,
                    "【" + columnName + " : " + str + "】不符合正则表达式: " + regexp);
        }
        return null;
    }

    /**
     * 首先会判断空值, 如果有空值直接返回空的 error
     *
     * 值包不含在 all 中， 返回 error
     *
     * 包含返回 null
     */
    public static ErrorMessage makeErrorIfNotContain(int raw, int columnIndex, String columnName,
            Object columnValue, List<String> all) {
        return makeErrorIfNotContain(raw, columnIndex, columnName, columnValue, all, "");
    }

    public static ErrorMessage makeErrorIfNotContain(int raw, int columnIndex, String columnName,
            Object columnValue, List<String> all, String messagePrefix) {
        ErrorMessage errorMessage = makeErrorIfBlank(raw, columnIndex, columnName, columnValue);
        if (errorMessage != null) {
            return errorMessage;
        }

        if (columnValue instanceof String) {
            columnValue = ((String) columnValue).trim();
            if (all == null || all.isEmpty()) {
                String msg = columnName + "出错:  不可以填 [" + columnValue + "] ";
                return new ErrorMessage(raw, columnIndex, columnName, messagePrefix + msg);
            }
            if (!all.contains((String) columnValue)) {
                String msg = columnName + "出错:  [" + columnValue + "] 不在下面范围 " + all + " 中";
                return new ErrorMessage(raw, columnIndex, columnName, messagePrefix + msg);
            }
        } else {
            throw new ExcelCommonException("不支持类型");
        }
        return null;
    }

    private static Object getFieldValue(IGetter fn, Object data) throws ReflectiveOperationException {
        Field field = getField(fn);
        field.setAccessible(true);
        return field.get(data);
    }

    private static SerializedLambda getSerializedLambda(Serializable fn)
            throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method method = fn.getClass().getDeclaredMethod("writeReplace");
        method.setAccessible(Boolean.TRUE);
        return (SerializedLambda) method.invoke(fn);
    }

    private static String toLowerCaseFirstOne(String field) {
        if (Character.isLowerCase(field.charAt(0))) {
            return field;
        } else {
            char firstOne = Character.toLowerCase(field.charAt(0));
            String other = field.substring(1);
            return firstOne + other;
        }
    }

    private static String getFieldName(IGetter fn) throws ReflectiveOperationException {
        return getField(fn).getName();
    }

    public static String getFieldNameByGetter(IGetter fn) {
        try {
            return getField(fn).getName();
        } catch (ReflectiveOperationException e) {
            throw new RuntimeException(e);
        }
    }

    public static Class<?> getFieldTtypeByGetter(IGetter fn) {
        try {
            return getField(fn).getType();
        } catch (ReflectiveOperationException e) {
            throw new RuntimeException(e);
        }
    }

    private static Field getField(IGetter fn) throws ReflectiveOperationException {
        SerializedLambda lambda = getSerializedLambda(fn);
        String methodName = lambda.getImplMethodName();
        String implClass = lambda.getImplClass().replace("/", ".");
        Class<?> tableClass = Class.forName(implClass);
        //使用的 lombok， 反过来一定真确
        String filedName = toLowerCaseFirstOne(methodName.substring(3));
        return tableClass.getDeclaredField(filedName);
    }

    public static String getExcelColumnName(IGetter fn, ParseContext context)
            throws ReflectiveOperationException {
        String fieldName = getFieldName(fn);
        return MapUtils.getString(context.getFieldNameAndColumnNameMap(), fieldName);
    }

    public static int getExcelColumnIndex(IGetter fn, ParseContext context)
            throws ReflectiveOperationException {
        String columnName = getExcelColumnName(fn, context);
        return MapUtils.getInteger(context.getColumnNameAndIndexMap(), columnName);
    }

    @FunctionalInterface
    public interface IGetter extends Serializable {

        Object get();
    }

    public Integer getRow() {
        return row;
    }

    public void setRow(Integer row) {
        this.row = row;
    }

    public Integer getCol() {
        return col;
    }

    public void setCol(Integer col) {
        this.col = col;
    }

    public String getColName() {
        return colName;
    }

    public void setColName(String colName) {
        this.colName = colName;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "ErrorMessage{"
                + "row=" + row
                + ", col=" + col
                + ", colName='" + colName + '\''
                + ", message='" + message + '\''
                + '}';
    }
}
