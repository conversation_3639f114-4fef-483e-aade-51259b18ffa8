package cloud.demand.lab.common.polaris;


import cloud.demand.lab.common.utils.EnvUtils;
import com.pugwoo.wooutils.net.NetUtils;
import com.tencent.polaris.api.core.ProviderAPI;
import com.tencent.polaris.api.exception.PolarisException;
import com.tencent.polaris.api.rpc.InstanceHeartbeatRequest;
import com.tencent.polaris.factory.api.APIFactory;
import com.tencent.polaris.factory.config.ConfigurationImpl;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Strings;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import yunti.boot.config.DynamicProperty;


@Slf4j
@Configuration
public class HeartBeat {

    private static final AtomicBoolean heartbeatFlag = new AtomicBoolean(false);

    @SuppressWarnings("BusyWait")
    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        if (!EnvUtils.isProduction()) {
            log.info("非生产环境,不设置北极星的心跳");
            return;
        }
        String namespace = "Production";
        String service = "cloud-demand-lab";
        List<String> ipv4IPs = tryGetIpv4Ips();
        if (ipv4IPs == null) {
            return;
        }
        String host = ipv4IPs.get(0);
        int port = 80;
        DynamicProperty<String> polarisToken = DynamicProperty.create("polaris.token", "");
        if (Strings.isBlank(polarisToken.get())) {
            log.error("北极星token 未配置,请配置token");
            return;
        }
        String token = polarisToken.get();

        ProviderAPI providerAPI;
        try {
            ConfigurationImpl configuration = new ConfigurationImpl();
            configuration.setDefault();
            providerAPI = APIFactory.createProviderAPIByConfig(configuration);
        } catch (PolarisException e) {
            log.error("北极星初始化出现了问题, 不启用心跳: {}", e.getMessage());
            return;
        }
        log.info("开启北极星心跳: namespace={}, service={}, ip={}, port={}", namespace, service, host, port);
        ProviderAPI finalProviderAPI = providerAPI;
        Thread heartbeatThread = new Thread(() -> {
            log.info("开始北极星心跳: namespace={}, service={}, ip={}, port={}", namespace, service, host, port);
            while (heartbeatFlag.get()) {
                try {
                    InstanceHeartbeatRequest instanceHeartbeatRequest = new InstanceHeartbeatRequest();
                    instanceHeartbeatRequest.setNamespace(namespace);
                    instanceHeartbeatRequest.setService(service);
                    instanceHeartbeatRequest.setHost(host);
                    instanceHeartbeatRequest.setPort(port);
                    instanceHeartbeatRequest.setToken(token);
                    // 此接口失败时，会抛出PolarisException异常。建议业务捕获异常输出日志，然后重试
                    finalProviderAPI.heartbeat(instanceHeartbeatRequest);
                } catch (PolarisException e) {
                    log.error("heartbeat for service {} failed: {}", service, e.getMessage());
                    // 心跳上报异常时，建议退避1s再执行下一次心跳
                    try {
                        Thread.sleep(3000);
                    } catch (InterruptedException ex) {
                        log.error("sleep 3s heartbeat for service {} interrupted: {}", service, e.getMessage());
                    }
                    continue;
                }
                // 心跳正常时，间隔TTL秒后再执行下一次心跳
                try {
                    Thread.sleep(30 * 1000L);
                } catch (InterruptedException e) {
                    log.error("sleep 30 heartbeat for service {} interrupted: {}", service, e.getMessage());
                }
            }
        });
        heartbeatFlag.set(true);
        heartbeatThread.start();
    }

    private static List<String> tryGetIpv4Ips() {
        List<String> ipv4IPs;
        try {
            ipv4IPs = NetUtils.getIpv4IPs();
        } catch (Exception e) {
            log.error("北极星心跳获取获取 ip 出现问题,不启动心跳: {}", e.getMessage());
            return null;
        }
        if (ipv4IPs == null || ipv4IPs.isEmpty()) {
            log.error("北极星心跳获取获取 ip 出现问题,不启动心跳");
            return null;
        }
        if (ipv4IPs.size() != 1) {
            log.warn("北极星心跳获取获取 ip 不是一个ip,默认使用第一个: {}", ipv4IPs.get(0));
        }
        return ipv4IPs;
    }

}
