package cloud.demand.lab.common.db_check;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("running_sql")
public class RunningSqlDO {

    /** 自增 id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 创建的时间，这个相当于请求时的时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private LocalDateTime createTime;

    /** app name<br/>Column: [app_name] */
    @Column(value = "app_name")
    private String appName;

    /** app name<br/>Column: [app_name] */
    @Column(value = "pod_name")
    private String podName;

    /** pod ip<br/>Column: [pod_ip] */
    @Column(value = "pod_ip")
    private String podIp;

    /** 当前正在执行的sql<br/>Column: [running_sql] */
    @Column(value = "running_sql")
    private String runningSql;

}