package cloud.demand.lab.common.entity;

import com.pugwoo.dbhelper.annotation.Column;
import java.util.Date;
import lombok.Data;

/**
 * 常用的基础DO：id、软删除、创建时间、修改时间
 */
@Data
public class BaseDO {

    /**
     * id<br/>Column: [id]
     */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**
     * 删除标记<br/>Column: [deleted]
     */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    /**
     * 创建时间<br/>Column: [create_time]
     */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /**
     * 更新时间<br/>Column: [update_time]
     */
    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;

    public void cleanBaseDO(){
        this.id = null;
        this.deleted = null;
        this.createTime = null;
        this.updateTime = null;
    }

}
