package cloud.demand.lab.common.config;

import com.alibaba.druid.pool.DruidDataSource;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.Map;

@Configuration
public class DruidCustomConfig {

    @Resource
    private ApplicationContext applicationContext;

    @PostConstruct
    public void configureDruid() {
        // 获取所有的 DruidDataSource 实例
        Map<String, DruidDataSource> dataSourceMap = applicationContext.getBeansOfType(DruidDataSource.class);
        for (Map.Entry<String, DruidDataSource> entry : dataSourceMap.entrySet()) {
            DruidDataSource druidDataSource = entry.getValue();
            druidDataSource.setTestWhileIdle(true);
            druidDataSource.setValidationQuery("SELECT 1");
        }
    }
}