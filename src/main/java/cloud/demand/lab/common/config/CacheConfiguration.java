package cloud.demand.lab.common.config;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.cache.HiSpeedCacheAspect;
import com.pugwoo.wooutils.redis.impl.JsonRedisObjectConverter;
import com.pugwoo.wooutils.utils.ClassUtils;
import com.pugwoo.wooutils.utils.InnerCommonUtils;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.mvel2.MVEL;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Configuration
@Slf4j
public class CacheConfiguration {

    /**
     * 注释缓存@HiSpeedCache
     */
    @Bean
    @ConditionalOnMissingBean
    HiSpeedCacheAspect hiSpeedCacheAspect() {
        return new HiSpeedCacheAspect();
    }



    @Retention(RetentionPolicy.RUNTIME)
    @Target({ElementType.METHOD})
    public @interface SynchronizedHiSpeedCache1Second {
        // 解决缓存击穿问题， 1秒内只有一次db 获取数据
    }

    @Aspect
    @Order(1500)
    @Component
    @EnableAspectJAutoProxy
    public static class AspectSynchronized1Second {
        private final Cache<String, Object> cache = CacheBuilder.newBuilder()
                .expireAfterWrite(1, TimeUnit.SECONDS) .build();
        @Around("@annotation(cloud.demand.lab.common.config.CacheConfiguration.SynchronizedHiSpeedCache1Second)")
        public  Object handle(ProceedingJoinPoint pjp) throws Throwable {
            MethodSignature signature = (MethodSignature) pjp.getSignature();
            Method targetMethod = signature.getMethod();
            HiSpeedCache hiSpeedCache = targetMethod.getAnnotation(HiSpeedCache.class);
            String key;
            try {
                key = generateKey(pjp, hiSpeedCache);
            } catch (Throwable e) {
                log.error("eval keyScript fail, keyScript:{}, args:{}, HiSpeedCache is disabled for this call.",
                        hiSpeedCache.keyScript(), JsonRedisObjectConverter.toJson(pjp.getArgs()));
                return pjp.proceed(); // 出现异常则等价于不使用缓存，直接调方法
            }
            String cacheKey = generateCacheKey(targetMethod, key);
            Optional<?> cachedValue = (Optional<?>) cache.get(cacheKey, () -> {
                try {
                    return  Optional.ofNullable(pjp.proceed());
                } catch (Throwable e) {
                    throw new RuntimeException(e);
                }
            });
            return cachedValue.orElse(null);
        }
    }
    private static String generateKey(ProceedingJoinPoint pjp, HiSpeedCache hiSpeedCache) {
        String key = "";

        String keyScript = hiSpeedCache.keyScript();
        if (InnerCommonUtils.isNotBlank(keyScript)) {
            Map<String, Object> context = new HashMap<>();
            context.put("args", pjp.getArgs()); // 类型是Object[]

            Object result = MVEL.eval(keyScript, context);
            if (result != null) { // 返回结果为null等价于keyScript为空字符串
                key = result.toString();
            }
        } else {
            // 当keyScript没有设置，而方法的参数的个数又不是0个时，打印告警日志，这种情况一般是有问题的
            if (pjp.getArgs() != null && pjp.getArgs().length > 0) {
                log.warn("HiSpeedCache keyScript is empty, while method args is not empty, method:{}, class:{}",
                        ((MethodSignature) pjp.getSignature()).getMethod().getName(),
                        pjp.getTarget().getClass().getName());
            }
        }

        return key;
    }

    private static String generateCacheKey(Method targetMethod, String key) {
        String methodSignatureWithClassName = ClassUtils.getMethodSignatureWithClassName(targetMethod);
        return "HSC:" + methodSignatureWithClassName + (key.isEmpty() ? "" : ":" + key);
    }

}
