package cloud.demand.lab.common.column_map.setter;

import cloud.demand.lab.common.column_map.ApplySetValueAnnotation;
import lombok.AllArgsConstructor;

public interface SetZoneInfoByZoneId extends ApplySetValueAnnotation {

    /**
     * 通过这个字段来映射值
     */
    Long getZoneId();


    @AllArgsConstructor
    enum SetZoneInfoEnum implements SetBySource<SetZoneInfoByZoneId> {
        /**
         * 给一个默认值空值不处理
         */
        NONE {
            public Object setBySource(SetZoneInfoByZoneId source) {
                return null;
            }
        },
        ZONE_NAME {
            public Object setBySource(SetZoneInfoByZoneId source) {
                Long zoneId = source.getZoneId();
                return "1" + zoneId;
            }
        };


    }
}
