package cloud.demand.lab.common.column_map.setter;

import cloud.demand.lab.common.column_map.ApplySetValueAnnotation;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekWithDateVO;
import cloud.demand.lab.modules.common_dict.service.ResPlanService;
import com.pugwoo.wooutils.lang.DateUtils;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;

public interface SetDateInfoByDate extends ApplySetValueAnnotation {

    /**
     * 通过这个字段来映射值
     */
    LocalDate getDate();

    enum SetDateInfoEnum implements SetBySource<SetDateInfoByDate> {
        NONE {
            public Object setBySource(SetDateInfoByDate source) {
                return null;
            }
        },
        YEAR {
            @Override
            public Object setBySource(SetDateInfoByDate source) {
                LocalDate date = source.getDate();
                if (date != null) {
                    return date.getYear();
                }
                return DEFAULT_VALUE;
            }
        },
        MONTH {
            public Object setBySource(SetDateInfoByDate source) {
                LocalDate date = source.getDate();
                if (date != null) {
                    return date.getMonthValue();
                }
                return DEFAULT_VALUE;
            }
        },
        YEAR_MONTH {
            @Override
            public Object setBySource(SetDateInfoByDate source) {
                if (source.getDate() != null) {
                    return YearMonth.of(source.getDate().getYear(), source.getDate().getMonthValue());
                }
                return "";
            }
        },
        /**
         * 假期周所属年
         */
        PLAN_WEEK_YEAR {
            @Override
            public Object setBySource(SetDateInfoByDate source) {
                LocalDate date = source.getDate();
                if (date == null) {
                    return DEFAULT_VALUE;
                }
                ResPlanHolidayWeekWithDateVO dateWithWeek = getPlanWeek(date.toString());
                return dateWithWeek.getYear();
            }
        },

        /**
         * 节假周所属月
         */
        PLAN_WEEK_MONTH {
            @Override
            public Object setBySource(SetDateInfoByDate source) {
                LocalDate date = source.getDate();
                if (date == null) {
                    return DEFAULT_VALUE;
                }
                ResPlanHolidayWeekWithDateVO dateWithWeek = getPlanWeek(date.toString());
                return dateWithWeek.getMonth();
            }
        },

        /**
         * 假期周第一天
         */
        PLAN_WEEK_START {
            @Override
            public Object setBySource(SetDateInfoByDate source) {
                LocalDate date = source.getDate();
                if (date == null) {
                    return DEFAULT_VALUE;
                }
                ResPlanHolidayWeekWithDateVO dateWithWeek = getPlanWeek(date.toString());
                return dateWithWeek.getStart();
            }
        },
        /**
         * 假期周最后一天
         */
        PLAN_WEEK_END {
            @Override
            public Object setBySource(SetDateInfoByDate source) {

                LocalDate date = source.getDate();
                if (date == null) {
                    return DEFAULT_VALUE;
                }
                ResPlanHolidayWeekWithDateVO dateWithWeek = getPlanWeek(date.toString());
                return dateWithWeek.getEnd();
            }
        },

        /**
         * 假期周
         */
        PLAN_WEEK {
            @Override
            public Object setBySource(SetDateInfoByDate source) {
                LocalDate date = source.getDate();
                if (date == null) {
                    return DEFAULT_VALUE;
                }
                ResPlanHolidayWeekWithDateVO dateWithWeek = getPlanWeek(date.toString());
                return dateWithWeek.getWeek();

            }
        },
        /**
         * 周几 1-7
         */
        PLAN_WEEK_NUM {
            @Override
            public Object setBySource(SetDateInfoByDate source) {
                return SetDateInfoEnum.nullIsDefault(source, (o) -> {
                    ResPlanHolidayWeekWithDateVO dateWithWeek = getPlanWeek(o.toString());
                    LocalDate weekStart = DateUtils.parseLocalDate(dateWithWeek.getStart());
                    return ChronoUnit.DAYS.between(weekStart, dateWithWeek.getDate()) + 1;
                });
            }
        };


        static ResPlanService resPlanService;
        private static ResPlanHolidayWeekWithDateVO getPlanWeek(String date) {
            if (resPlanService == null) {
                resPlanService = SpringUtil.getBean(ResPlanService.class);
            }
            return resPlanService.getHolidayWeekInfoByDate(date);
        }

        private static Object nullIsDefault(SetDateInfoByDate source, Function<SetDateInfoByDate, Object> setBySource) {
            if (source.getDate() == null) {
                return DEFAULT_VALUE;
            }
            return setBySource.apply(source);
        }
    }

}
