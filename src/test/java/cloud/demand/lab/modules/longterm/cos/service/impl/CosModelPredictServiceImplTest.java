package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputScaleDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutScaleByAlgorithmDO;
import cloud.demand.lab.modules.longterm.cos.service.CosBigCustomerHistoryChangeService;
import cloud.demand.lab.modules.longterm.cos.service.CosCreatePredictTaskService;
import cloud.demand.lab.modules.longterm.cos.web.req.ReCalculateAlgorithmPredictReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.ReCalculateAlgorithmPredictResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryBigCustomerHistoryChangeResp;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CosModelPredictServiceImpl 测试类
 */
public class CosModelPredictServiceImplTest {

    @Mock
    private DBHelper planCosDBHelper;

    @Mock
    private DBHelper cdLabDbHelper;

    @Mock
    private CosBigCustomerHistoryChangeService cosBigCustomerHistoryChangeService;

    @Mock
    private CosCreatePredictTaskService cosCreatePredictTaskService;

    @InjectMocks
    private CosModelPredictServiceImpl cosModelPredictService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testReCalculateAlgorithmPredict_Success() {
        // 准备测试数据
        Long categoryId = 1L;
        Long taskId = 100L;
        
        ReCalculateAlgorithmPredictReq req = new ReCalculateAlgorithmPredictReq();
        req.setCategoryId(categoryId);
        req.setTaskId(taskId);

        // Mock categoryConfig
        CosLongtermPredictCategoryConfigDO categoryConfig = new CosLongtermPredictCategoryConfigDO();
        categoryConfig.setId(categoryId);
        categoryConfig.setWhereSql("1=1");
        categoryConfig.setPredictEnd("2024-12");
        categoryConfig.setLinearStartDate(LocalDate.of(2023, 1, 1));
        categoryConfig.setArimaArgs("1,2,1");

        // Mock 大客户历史变化数据
        QueryBigCustomerHistoryChangeResp bigCustomerResp = new QueryBigCustomerHistoryChangeResp();
        bigCustomerResp.setDataList(new ArrayList<>());

        // Mock 历史数据 - 创建一些测试数据
        List<Object> mockHistoryData = new ArrayList<>();
        // 这里可以添加一些mock数据，但为了简化测试，我们使用空列表

        // 设置Mock行为
        when(cosCreatePredictTaskService.getCategoryById(categoryId)).thenReturn(categoryConfig);
        when(cosBigCustomerHistoryChangeService.queryBigCustomerHistoryChange(any())).thenReturn(bigCustomerResp);
        when(planCosDBHelper.getRaw(any(), anyString())).thenReturn(mockHistoryData);
        when(cdLabDbHelper.delete(any(), anyString(), any())).thenReturn(1);
        when(cdLabDbHelper.insertBatchWithoutReturnId(any())).thenReturn(1);

        // 执行测试
        ReCalculateAlgorithmPredictResp result = cosModelPredictService.reCalculateAlgorithmPredict(req);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getIsSuccess());

        // 验证方法调用
        verify(cosCreatePredictTaskService, times(1)).getCategoryById(categoryId);
        verify(cosBigCustomerHistoryChangeService, times(1)).queryBigCustomerHistoryChange(any());
        // 由于没有历史数据，delete和insert可能不会被调用，所以我们只验证核心逻辑被执行了
        verify(planCosDBHelper, times(1)).getRaw(any(), anyString());
    }

    @Test
    void testReCalculateAlgorithmPredict_CategoryNotFound() {
        // 准备测试数据
        Long categoryId = 999L;
        Long taskId = 100L;
        
        ReCalculateAlgorithmPredictReq req = new ReCalculateAlgorithmPredictReq();
        req.setCategoryId(categoryId);
        req.setTaskId(taskId);

        // 设置Mock行为 - 方案不存在
        when(cosCreatePredictTaskService.getCategoryById(categoryId)).thenReturn(null);

        // 执行测试
        ReCalculateAlgorithmPredictResp result = cosModelPredictService.reCalculateAlgorithmPredict(req);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.getIsSuccess());

        // 验证方法调用
        verify(cosCreatePredictTaskService, times(1)).getCategoryById(categoryId);
        verify(cosBigCustomerHistoryChangeService, never()).queryBigCustomerHistoryChange(any());
    }

    @Test
    void testReCalculateAlgorithmPredict_Exception() {
        // 准备测试数据
        Long categoryId = 1L;
        Long taskId = 100L;
        
        ReCalculateAlgorithmPredictReq req = new ReCalculateAlgorithmPredictReq();
        req.setCategoryId(categoryId);
        req.setTaskId(taskId);

        // 设置Mock行为 - 抛出异常
        when(cosCreatePredictTaskService.getCategoryById(categoryId)).thenThrow(new RuntimeException("Database error"));

        // 执行测试
        ReCalculateAlgorithmPredictResp result = cosModelPredictService.reCalculateAlgorithmPredict(req);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.getIsSuccess());

        // 验证方法调用
        verify(cosCreatePredictTaskService, times(1)).getCategoryById(categoryId);
    }
}
